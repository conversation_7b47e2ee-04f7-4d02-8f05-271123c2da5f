package utils

import (
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"math/rand"
	"time"
)

var releaseLockScript = `
        if redis.call("get", KEYS[1]) == ARGV[1] then
            return redis.call("del", KEYS[1])
        else
            return 0
        end
    `

func ReleaseLockByValue(ctx *gin.Context, lockKey, lockValue string) (bool, error) {
	// 使用Lua脚本来安全释放锁
	result, err := helpers.RedisClient.Lua(ctx, releaseLockScript, 1, lockKey, lockValue)
	if err != nil {
		return false, err
	}
	return result == 1, nil
}

func LockRetry(ctx *gin.Context, key string, value interface{}, expire uint64, retry int) (result bool, err error) {
	if retry > 10 {
		zlog.Infof(ctx, "重试次数不能超过10次，入参为%+v。", retry)
		retry = 10
	}
	count := 0
	for count < retry {
		result, err = helpers.RedisClient.SetNxByEX(ctx, key, value, expire)
		if err != nil {
			zlog.Warnf(ctx, "获取分布式锁异常，key：%+v，value：%+v", key, value)
			count += 1
			time.Sleep(time.Millisecond * 300)
			continue
		}
		if result {
			return
		}
		zlog.Infof(ctx, "获取分布式锁失败，key：%+v，value：%+v", key, value)
		count += 1
		time.Sleep(time.Millisecond * 300)
	}
	return
}

func GetSpinLock(ctx *gin.Context, key, value string, expireSeconds, spinTimeoutSeconds uint64) (result bool, err error) {
	_rand := rand.New(rand.NewSource(time.Now().UnixNano()))
	randInt := func(a, b int) int {
		return _rand.Intn(b-a+1) + a
	}
	startTime := time.Now()
	for retries := 1; ; retries++ {
		result, err = helpers.RedisClient.SetNxByEX(ctx, key, value, expireSeconds)
		if err == nil && result { // 加锁成功
			return
		}

		if time.Since(startTime) >= time.Duration(spinTimeoutSeconds)*time.Second {
			zlog.Infof(ctx, "GetSpinLock timeout, key：%+v，value：%+v, retries:%+v, err:%+v", key, value, retries, err)
			return false, err
		}

		// 使用退避抖动
		base, _cap := 16, 256
		sleep := randInt(base, min(_cap, base*(1<<min(30, retries))))
		time.Sleep(time.Duration(sleep) * time.Millisecond)
	}
}
