package dtostudent

import "assistantdeskgo/utils"

type StudentDetailRes struct {
	StudentUid          int            `json:"studentUid"`
	EncryptedStudentUid string         `json:"encryptedStudentUid"`
	StudentName         string         `json:"studentName"`
	NamePinyin          []utils.Pinyin `json:"namePinyin"`
	Nickname            string         `json:"nickname"`
	Sex                 int            `json:"sex"`
	Grade               string         `json:"grade"`
	School              string         `json:"school"`
	Phone               string         `json:"phone"`
	Area                string         `json:"area"`
	AssistantUid        int64          `json:"assistantUid"`
	AssistantPhone      string         `json:"assistantPhone"`
	BelongObj           BelongerObj    `json:"belongObj"`
	DeviceInfo          string         `json:"deviceInfo"`
}
type BelongerObj struct {
	Belongval   int    `json:"belongVal"` //belonger
	BelongerStr string `json:"belongStr"` //belongerStr
}

type WXBelongerRes struct {
	Result bool `json:"result"`
}
type WXBelongerMapRes struct {
	Label string `json:"label"`
	Value int    `json:"value"`
}
type WXBelongerMapListRes struct {
	List []WXBelongerMapRes `json:"list"`
}

type StudentUidByWxIdRes struct {
	StudentUid int `json:"studentUid"`
}

type GetStudentByPhoneRsp struct {
	StudentInfos []GetStudentInfo `json:"studentInfos"`
}

type GetStudentByUidRsp struct {
	StudentInfos []GetStudentInfo `json:"studentInfos"`
}

type GetStudentInfo struct {
	StudentUid  int64  `json:"studentUid"`
	Phone       string `json:"phone"`
	StudentName string `json:"studentName"`
	Avatar      string `json:"avatar"`
}

type GetStudentDataRsp struct {
	CourseName                string `json:"courseName"`
	AssistantDeviceName       string `json:"assistantDeviceName"`
	AssistantPersonName       string `json:"assistantPersonName"`
	StudentName               string `json:"studentName"`
	StudentUid                int64  `json:"studentUid" form:"studentUid"`
	LearnCodeDayCnt           int64  `json:"learnCodeDayCnt"`
	LessonWatchTime           int64  `json:"lessonWatchTime"` //
	RoomAttendDuration        string `json:"roomAttendDuration"`
	PlayContentTime           string `json:"playContentTime"`
	LessonIsFinishedTheCourse int64  `json:"lessonIsFinishedTheCourse"`
	InclassParticipateCnt     int64  `json:"inclassParticipateCnt"`
	PlaybackParticipateCnt    int64  `json:"playbackParticipateCnt"`
	XlbcHomeworkCorrectStatus int64  `json:"xlbcHomeworkCorrectStatus"`
	XlbcHomeworkCorrectTime   int64  `json:"xlbcHomeworkCorrectTime"`
	XlbcHomeworkLevel         int64  `json:"xlbcHomeworkLevel"`
	XlbcHomeworkSubmitStatus  int64  `json:"xlbcHomeworkSubmitStatus"`
	XlbcHomeworkSubmitTime    int64  `json:"xlbcHomeworkSubmitTime"`
}
