package dtostudent

type StudentDetailReq struct {
	StudentUid      int64  `json:"studentUid" form:"studentUid" required:"true"`
	AssistantUid    int64  `json:"assistantUid" form:"assistantUid" required:"true"`
	PersonUid       int64  `json:"personUid" form:"personUid" required:"true"`
	StudentRemoteId string `json:"studentRemoteId" form:"studentRemoteId" required:"true"`
}

type StudentUidByWxIdReq struct {
	AssistantUid string `json:"assistantUid" form:"assistantUid" required:"true"`
	WeixinId     string `json:"weixinId" form:"weixinId" required:"true"`
	PersonUid    int64  `json:"personUid" form:"personUid" required:"true"`
}

type WXBelongerReq struct {
	StudentUid   string `json:"studentUid" form:"studentUid" required:"true"`
	AssistantUid string `json:"assistantUid" form:"assistantUid" required:"true"`
	Belonger     int64  `json:"belonger" form:"belonger" required:"true"`
	WeixinId     string `json:"weixinId" form:"weixinId" required:"true"`
}

type GetStudentByPhoneReq struct {
	AssistantUid int64  `json:"assistantUid" form:"assistantUid"`
	Phone        string `json:"phone" form:"phone"`
}

type GetStudentByUidReq struct {
	AssistantUid int64 `json:"assistantUid" form:"assistantUid"`
	StudentUid   int64 `json:"studentUid" form:"studentUid"`
}

type GetStudentDataReq struct {
	StudentUid int64 `json:"studentUid" form:"studentUid" required:"true"`
	CourseId   int64 `json:"courseId" form:"courseId" required:"true"`
	LessonId   int64 `json:"lessonId" form:"lessonId"`
}
