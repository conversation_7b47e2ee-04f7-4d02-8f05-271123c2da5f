package dtonotifyignore

type AddNotifyIgnoreReq struct {
	CourseId        int64   `json:"courseId" form:"courseId"`               // 课程 id
	StudentUids     []int64 `json:"studentUids" form:"studentUids"`         // 学生ID
}

// DeleteNotifyIgnoreReq 删除消息免打扰配置请求
type DeleteNotifyIgnoreReq struct {
	CourseId    int64   `json:"courseId" binding:"required"`
	StudentUids []int64 `json:"studentUids" binding:"required"`
}

// ListNotifyIgnoreReq 获取消息免打扰配置列表请求
type ListNotifyIgnoreReq struct {
	CourseId     int64 `json:"courseId" form:"courseId"`         // 课程 id
	AssistantUid int64 `json:"assistantUid" form:"assistantUid"` // 资产 id
}
