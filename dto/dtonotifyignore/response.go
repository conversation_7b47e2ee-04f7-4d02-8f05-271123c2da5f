package dtonotifyignore

type AddNotifyIgnoreResp struct {
}

// DeleteNotifyIgnoreResp 删除消息免打扰配置响应
type DeleteNotifyIgnoreResp struct {
}

// ListNotifyIgnoreResp 获取消息免打扰配置列表响应
type ListNotifyIgnoreResp struct {
	List  []NotifyIgnoreItem `json:"list"`
	Count int64              `json:"count"`
}

// NotifyIgnoreItem 消息免打扰配置项
type NotifyIgnoreItem struct {
	StudentUid  int64  `json:"studentUid"`  // 学生ID
	StudentName string `json:"studentName"` // 学生姓名
	ClassName   string `json:"className"`   // 班级名称
	Phone       string `json:"phone"`       // 手机号
	StartTime   string `json:"startTime"`   // 开始时间
	EndTime     string `json:"endTime"`     // 结束时间
	Operator    string `json:"operator"`    // 操作人
	OperatorUid int64  `json:"operatorUid"` // 操作人ID
}
