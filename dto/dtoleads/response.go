package dtoleads

import (
	"assistantdeskgo/api/dataproxy"
)

type GetLeadsLayerDetailInfoRsp struct {
	Features           []dataproxy.Feature `json:"features"`
	PurchaseIntentions []PurchaseIntention `json:"purchaseIntentions"`
	Milestones         []Milestone         `json:"milestones"`
	RefreshTime        string              `json:"refreshTime"`
	TransLevel         string              `json:"transLevel"`
}

type PurchaseIntention struct {
	Date  string  `json:"date"`
	Score float64 `json:"score"`
}

type Milestone struct {
	Date string `json:"date"`
	Desc string `json:"desc"`
}
