package dtotool

type CreateTaskReq struct {
	TaskType  int64       `json:"taskType" form:"taskType"`
	Operator  string      `json:"operator" form:"operator"`
	TaskParam interface{} `json:"taskParam" form:"taskParam"`
}

type CreateOrderTaskParam struct {
	GradeIds     []int64 `json:"gradeIds"`
	LearnSeasons []int64 `json:"learnSeasons"`
	Year         int64   `json:"year"`
	SubjectIds   []int64 `json:"subjectIds"`
	SkuIds       []int64 `json:"skuIds"`
	SearchTimes  int64   `json:"searchTimes"`
}

type InClassTaskParam struct {
	Date string `json:"date"` // Ymd格式
}

type CreateTaskRsp struct {
	TaskId int64 `json:"taskId" form:"taskId"`
}

type DownLoadTaskCsvReq struct {
	TaskType int64 `json:"taskType" form:"taskType"`
	TaskId   int64 `json:"taskId" form:"taskId"`
}

type FailTaskCsvReq struct {
	Status   int64 `json:"status" form:"status"`
	TaskId   int64 `json:"taskId" form:"taskId"`
	TaskType int64 `json:"taskType" form:"taskType"`
}

type DownLoadTaskCsvRsp struct {
	CreateOrderUrl  string `json:"createOrderUrl" form:"createOrderUrl"`
	CallOutUrl      string `json:"callOutUrl" form:"callOutUrl"`
	WxStudentUrl    string `json:"wxStudentUrl" form:"wxStudentUrl"`
	WxXXZStudentUrl string `json:"wxXXZStudentUrl" form:"wxXXZStudentUrl"`
	ReadApiUrl      string `json:"readApiUrl" form:"readApiUrl"`
	InClassUrl      string `json:"inClassUrl" form:"inClassUrl"`
}
