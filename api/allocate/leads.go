package allocate

import (
	"assistantdeskgo/api/apis"
	"fmt"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const UriGetLeadsByUid = "/allocate/api/getleadsbyuid"
const UriGetNormalLeadsByCourseAssistant = "/allocate/api/getnormalleadsbycourseassistant"

type GetLeadsByUidReq struct {
	StuUid int64 `json:"stuUid"`
}

type GetLeadsByUidRsp struct {
	List []StuLeadsInfo `json:"list"`
}

type StuLeadsInfo struct {
	LeadsId    int   `json:"leadsId"`
	StudentUid int64 `json:"studentUid"`
	CourseId   int64 `json:"courseId"`
	UserId     int64 `json:"userId"`
	PersonId   int64 `json:"personId"`
}

func GetLeadsByUid(ctx *gin.Context, studentUid int64) (result []StuLeadsInfo, err error) {
	req := GetLeadsByUidReq{
		StuUid: studentUid,
	}
	resp := GetLeadsByUidRsp{}
	err = apis.Do(ctx, req, &resp)
	if err != nil {
		zlog.Warnf(ctx, "GetLeadsByUid failed, req: %+v, resp: %+v, err: %+v", req, resp, err)
		return
	}
	result = resp.List
	return
}

func GetCourseIdByLeads(leadsList []StuLeadsInfo, assistantUid int64) int64 {
	var courseId int64
	if len(leadsList) <= 0 {
		return courseId
	}
	for _, leadsInfo := range leadsList {
		if leadsInfo.UserId == assistantUid {
			courseId = leadsInfo.CourseId
			break
		}
	}
	return courseId
}

type GetNormalLeadsByCourseAssistantReq struct {
	CourseId     int64 `json:"courseId"`
	AssistantUid int64 `json:"assistantUid"`
	Page         int64 `json:"page"`
	PageSize     int64 `json:"pageSize"`
}

type GetNormalLeadsByCourseAssistantRsp struct {
	List     []GetNormalLeadsByCourseAssistantItem `json:"list"`
	PageSize int64                                 `json:"pageSize"`
}

type GetNormalLeadsByCourseAssistantItem struct {
	StudentUid   int64  `json:"studentUid"`
	CourseId     int64  `json:"courseId"`
	LeadsId      int64  `json:"leadsId"`
	PersonUid    int64  `json:"personUid"`
	ClassId      int64  `json:"classId"`
	StudentType  int64  `json:"studentType"`
	TransferType int64  `json:"transferType"`
	Grade        int64  `json:"grade"`
	Subject      int64  `json:"subject"`
	IsOriginal   int64  `json:"isOriginal"`
	Status       int64  `json:"status"`
	TradeTime    int64  `json:"tradeTime"`
	RefundTime   int64  `json:"refundTime"`
	ExtData      string `json:"extData"`
	UserId       int64  `json:"userId"`
	WxMapId      int64  `json:"wxMapId"`
}

func GetNormalLeadsByCourseAssistant(ctx *gin.Context, courseId, assistantUid int64) (result []GetNormalLeadsByCourseAssistantItem, err error) {
	if courseId <= 0 || assistantUid <= 0 {
		return nil, fmt.Errorf("invalid params")
	}

	page := int64(1)
	pageSize := int64(1000)
	result = make([]GetNormalLeadsByCourseAssistantItem, 0)

	// 最多取2次，2000条
	for i := 1; i <= 2; i++ {
		req := GetNormalLeadsByCourseAssistantReq{
			CourseId:     courseId,
			AssistantUid: assistantUid,
			Page:         page,
			PageSize:     pageSize,
		}

		resp := GetNormalLeadsByCourseAssistantRsp{}
		err = apis.Do(ctx, req, &resp)
		if err != nil {
			zlog.Warnf(ctx, "GetNormalLeadsByCourseAssistant failed, req: %+v, resp: %+v, err: %+v", req, resp, err)
			return nil, err
		}

		if len(resp.List) == 0 {
			break
		}

		result = append(result, resp.List...)

		if resp.PageSize < pageSize {
			break
		}

		page++
	}

	return result, nil
}
