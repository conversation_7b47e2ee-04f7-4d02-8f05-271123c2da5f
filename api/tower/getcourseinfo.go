package tower

import (
	"assistantdeskgo/api/apis"
	"assistantdeskgo/utils"
	"fmt"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	UriGetCourseInfo             = "/tower/api/getcourseinfo"
	UriGetCourseBindByDeviceUids = "/tower/api/getcoursebindbydeviceuids"
	UriGetBatchCourseExpireTime  = "/tower/api/getbatchexpiretimebycourselist"
	UriGetCourseBindByCourseIds  = "/tower/api/getcoursebindbycourseids"
	UriGetClassCode              = "/tower/api/getclasscode"
)

type GetCourseInfoReq struct {
	CourseId int64 `json:"courseId"`
}

type GetCourseBindByCourseIdsReq struct {
	CourseIds string `json:"courseIds" form:"courseIds"`
}

type GetCourseBindByCourseIdsRsp struct {
	CourseBindData map[string][]struct {
		CourseId   int64 `json:"courseId"`
		DeviceUid  int64 `json:"deviceUid"`
		Status     int64 `json:"status"`
		MapId      int64 `json:"mapId"`
		CreateTime int64 `json:"createTime"`
	} `json:"courseBindData"`
}

type GetCourseInfoRsp struct {
	ID             int64 `json:"id"`
	CourseID       int64 `json:"courseId"`
	CoursePriceTag int64 `json:"coursePriceTag"`
	//TeacherTagName   string             `json:"teacherTagName"`
	//GroupDesc        string             `json:"groupDesc"`
	//CreateTime       int64              `json:"createTime"`
	//UpdateTime       int64              `json:"updateTime"`
	//Status           int64              `json:"status"`
	//GroupServiceList []GroupServiceInfo `json:"groupServiceList"`
	//TaskID           int64              `json:"taskId"`
	//CourseGroupType  int64              `json:"courseGroupType"`
	//TaskStatus       int64              `json:"taskStatus"`
	//Subject          int64              `json:"subject"`
	//Grade            int64              `json:"grade"`
	//Period           int64              `json:"period"`
	//Year             int64              `json:"year"`
	//Season           int64              `json:"season"`
	//CourseType       int64              `json:"courseType"`
	//Ext              string             `json:"ext"`
	//Department       int64              `json:"department"`
	//SaleMode         int64              `json:"saleMode"`
	//IsInner          int64              `json:"isInner"`
	//OperatorUID      int64              `json:"operatorUid"`
	//Operator         string             `json:"operator"`
	//PullNewDuty      int64              `json:"pullNewDuty"`
	//NewCourseType    int64              `json:"newCourseType"`
	//Frequency        int64              `json:"frequency"`
	//AreaSet          string             `json:"area_set"`
	//OpenStatus       int64              `json:"open_status"`
}

type GroupServiceInfo struct {
	ID               int    `json:"id"`
	GroupServiceName string `json:"groupServiceName"`
	IconURL          string `json:"iconUrl"`
}

func GetCourseInfo(ctx *gin.Context, courseId int64) (*GetCourseInfoRsp, error) {
	params := GetCourseInfoReq{CourseId: courseId}

	rsp := &GetCourseInfoRsp{}
	if err := apis.Do(ctx, params, rsp); err != nil {
		zlog.Warnf(ctx, "GetCourseInfo request failed, courseId: %d, err: %+v", courseId, err)
		return nil, err
	}

	return rsp, nil
}

type GetCourseBindByDeviceUidReq struct {
	DeviceUids string `json:"deviceUids"`
	Year       int64  `json:"year"`
}

type GetCourseBindByDeviceUidResp struct {
	CourseBindData map[int64][]CourseBind `json:"courseBindData""`
}

type CourseBind struct {
	CourseId  int64 `json:"courseId"`
	DeviceUid int64 `json:"deviceUid"`
	Status    int64 `json:"status"`
	MapId     int64 `json:"mapId"`
}

func GetCourseBindByDeviceUid(ctx *gin.Context, deviceUids []int, year int64) (*GetCourseBindByDeviceUidResp, error) {
	params := GetCourseBindByDeviceUidReq{DeviceUids: utils.JoinArrayIntToString(deviceUids, ",")}
	if year > 0 {
		params.Year = year
	}
	rsp := &GetCourseBindByDeviceUidResp{}
	if err := apis.Do(ctx, params, rsp); err != nil {
		zlog.Warnf(ctx, "GetCourseBindByDeviceUid request failed, deviceUids: %+v, err: %+v", deviceUids, err)
		return nil, err
	}

	return rsp, nil
}

type CourseExpireTimeReq struct {
	CourseIds string `json:"courseIds" form:"courseIds"`
}

type CourseExpireTime struct {
	CourseId        int64 `json:"courseId"`
	ExpireTime      int64 `json:"expireTime"`
	ExpireTimeStart int64 `json:"expireTimeStart"`
}

type CourseExpireTimeRsp []CourseExpireTime

func GetBatchExpireTimeByCourseIds(ctx *gin.Context, courseIds []int64) (rsp []CourseExpireTime, err error) {
	req := CourseExpireTimeReq{
		CourseIds: utils.JoinArrayInt64ToString(courseIds, ","),
	}
	err = apis.Post(ctx, UriGetBatchCourseExpireTime, &req, &rsp, apis.WithEncode(apis.EncoderForm))

	return rsp, err
}

func GetCourseBindByCourseIds(ctx *gin.Context, courseIds []int64) (rsp *GetCourseBindByCourseIdsRsp, err error) {
	params := GetCourseBindByCourseIdsReq{CourseIds: utils.Int64ArrayToString(courseIds)}
	rsp = &GetCourseBindByCourseIdsRsp{}
	if err = apis.Do(ctx, params, rsp); err != nil {
		zlog.Warnf(ctx, "GetCourseInfo request failed, courseId: %d, err: %+v", courseIds, err)
	}
	return
}

type GetClassCodeReq struct {
	CourseId     int64 `json:"courseId"`
	AssistantUid int64 `json:"assistantUid"`
}

type GetClassCodeRsp struct {
	CourseId     int64                   `json:"courseId"`
	AssistantUid int64                   `json:"assistantUid"`
	List         map[int64]int64         `json:"list"`
	ClassList    []GetClassCodeClassInfo `json:"classList"`
}

type GetClassCodeClassInfo struct {
	ClassId       int64 `json:"classId"`
	Code          int64 `json:"code"`
	StudentMaxCnt int64 `json:"studentMaxCnt"`
}

func GetGetClassCode(ctx *gin.Context, courseId int64, assistantUid int64) (result map[int64]string, err error) {
	req := GetClassCodeReq{
		CourseId:     courseId,
		AssistantUid: assistantUid,
	}
	resp := GetClassCodeRsp{}
	err = apis.Do(ctx, req, &resp)
	if err != nil {
		zlog.Warnf(ctx, "GetGetClassCode failed, req: %+v, resp: %+v, err: %+v", req, resp, err)
		return
	}

	result = make(map[int64]string)
	for classId, code := range resp.List {
		if code > 0 {
			result[classId] = fmt.Sprintf("%d班", code)
		} else {
			result[classId] = "未分班"
		}
	}
	return
}
