package touchmisgo

import (
	"assistantdeskgo/api/apis"
	"net/http"
)

func init() {
	apis.Register(GetCallRecordInfoUrl, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetCallRecordInfoReq{},
		Encoder:  apis.EncoderJson,
		Response: map[int64]GetCallRecordInfo{},
	})
	apis.Register(GetCallRecordHistoryUrl, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetCallRecordHistoryReq{},
		Encoder:  apis.EncoderJson,
		Response: GetCallRecordHistoryRsp{},
	})
	apis.Register(UriSendGroupMsg, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  SendGroupMsgReq{},
		Encoder:  apis.EncoderJson,
		Response: SendGroupMsgRsp{},
	})
	apis.Register(GetCardCheckUrl, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  CardCheckMsgReq{},
		Encoder:  apis.EncoderJson,
		Response: WeChatCheckRes{},
	})
	apis.Register(GetCheckRelationUrl, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  CheckRelationReq{},
		Encoder:  apis.EncoderJson,
		Response: CheckRelationRes{},
	})
	apis.Register(GetCardSendUrl, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  CardSendReq{},
		Encoder:  apis.EncoderJson,
		Response: CardSendRes{},
	})
	apis.Register(GetCallRecordListUrl, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetCallRecordListReq{},
		Encoder:  apis.EncoderJson,
		Response: GetCallRecordHistoryResp{},
	})
	apis.Register(apiSmsSend, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  SmsSendReq{},
		Encoder:  apis.EncoderJson,
		Response: SmsSendResp{},
	})
	apis.Register(GetCardSendLimitUrl, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  CardSendLimitReq{},
		Encoder:  apis.EncoderJson,
		Response: CardSendLimitRes{},
	})
	apis.Register(apiGetDeviceBindInfo, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  GetDeviceBindInfoReq{},
		Encoder:  apis.EncoderForm,
		Response: GetDeviceBindInfoResp{},
	})
	apis.Register(NotifyIgnoreAddUrl, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  NotifyIgnoreAddReq{},
		Encoder:  apis.EncoderJson,
		Response: NotifyIgnoreAddResp{},
	})
	apis.Register(NotifyIgnoreDeleteUrl, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  NotifyIgnoreDeleteReq{},
		Encoder:  apis.EncoderJson,
		Response: NotifyIgnoreDeleteResp{},
	})
	apis.Register(NotifyIgnoreQueryUrl, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  NotifyIgnoreQueryReq{},
		Encoder:  apis.EncoderJson,
		Response: NotifyIgnoreQueryResp{},
	})
	apis.Register(NotifyIgnoreFilterUrl, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  NotifyIgnoreFilterReq{},
		Encoder:  apis.EncoderJson,
		Response: NotifyIgnoreFilterResp{},
	})

}
