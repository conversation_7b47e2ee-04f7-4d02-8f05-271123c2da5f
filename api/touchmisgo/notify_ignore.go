package touchmisgo

import (
	"assistantdeskgo/api/apis"
	"assistantdeskgo/utils"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const NotifyIgnoreAddUrl = "/touchmisgo/notify/ignore/add"
const NotifyIgnoreDeleteUrl = "/touchmisgo/notify/ignore/delete"
const NotifyIgnoreQueryUrl = "/touchmisgo/notify/ignore/query"
const NotifyIgnoreFilterUrl = "/touchmisgo/notify/ignore/filter"

type NotifyIgnoreAddReq struct {
	SourceId        int64   `json:"sourceId" form:"sourceId"`               // 配置ID，例如课程 id
	SourceType      string  `json:"sourceType" form:"sourceType"`           // 配置类型，course: 课程设置
	IgnoreStartTime int64   `json:"ignoreStartTime" form:"ignoreStartTime"` // 生效开始时间
	IgnoreEndTime   int64   `json:"ignoreEndTime" form:"ignoreEndTime"`     // 生效结束时间
	Creator         int64   `json:"creator" form:"creator"`                 // 创建者，真人 id
	StudentIds      []int64 `json:"studentIds" form:"studentIds"`           // 学生ID列表
}

type NotifyIgnoreDeleteReq struct {
	SourceId   int64   `json:"sourceId" form:"sourceId"`     // 配置ID，例如课程 id
	SourceType string  `json:"sourceType" form:"sourceType"` // 配置类型，course: 课程设置
	Operator   int64   `json:"operator" form:"operator"`     // 操作者 id，真人 id
	StudentIds []int64 `json:"studentIds" form:"studentIds"` // 学生ID列表
}

type NotifyIgnoreQueryReq struct {
	SourceId   int64   `json:"sourceId" form:"sourceId"`     // 配置ID，例如课程 id
	SourceType string  `json:"sourceType" form:"sourceType"` // 配置类型，course: 课程设置
	StudentIds []int64 `json:"studentIds" form:"studentIds"` // 学生ID列表，一批最多 200 个
}

type NotifyIgnoreFilterReq struct {
	SourceId     int64   `json:"sourceId" form:"sourceId"`         // 配置ID，例如课程 id
	SourceType   string  `json:"sourceType" form:"sourceType"`     // 配置类型，course: 课程设置
	StudentIds   []int64 `json:"studentIds" form:"studentIds"`     // 学生ID列表，一批最多 200 个
	AssistantUid int64   `json:"assistantUid" form:"assistantUid"` // 助教 uid
}

type NotifyIgnoreItem struct {
	StudentId       int64  `json:"studentId"`       // 学生 id
	SourceId        int64  `json:"sourceId"`        // 配置ID
	SourceType      string `json:"sourceType"`      // 配置类型
	IgnoreStartTime int64  `json:"ignoreStartTime"` // 屏蔽开始时间
	IgnoreEndTime   int64  `json:"ignoreEndTime"`   // 屏蔽结束时间
	Operator        int64  `json:"operator"`        // 创建者
	IsNotifyIgnore  bool   `json:"isNotifyIgnore"`  // true 表示屏蔽开启, false 表示未屏蔽
}

type NotifyIgnoreAddResp struct {
}
type NotifyIgnoreDeleteResp struct {
}

type NotifyIgnoreQueryResp struct {
	NotifyIgnoreList []NotifyIgnoreItem `json:"notifyIgnoreList"`
}

type NotifyIgnoreFilterResp struct {
	NotifyStudentIds []int64 `json:"notifyStudentIds"`
}

func NotifyIgnoreAdd(ctx *gin.Context, req NotifyIgnoreAddReq) (*NotifyIgnoreAddResp, error) {
	// 如果学生ID列表为空，直接返回
	if len(req.StudentIds) == 0 {
		return &NotifyIgnoreAddResp{}, nil
	}

	// 将学生ID列表分批，每批最多300个
	batches := utils.ChunkArrayInt64(req.StudentIds, 300)
	resp := &NotifyIgnoreAddResp{}

	// 对每一批学生ID进行处理
	for _, batch := range batches {
		batchReq := NotifyIgnoreAddReq{
			SourceId:        req.SourceId,
			SourceType:      req.SourceType,
			IgnoreStartTime: req.IgnoreStartTime,
			IgnoreEndTime:   req.IgnoreEndTime,
			Creator:         req.Creator,
			StudentIds:      batch,
		}

		// 发送请求
		batchResp := &NotifyIgnoreAddResp{}
		if err := apis.Do(ctx, batchReq, batchResp); err != nil {
			zlog.Warnf(ctx, "NotifyIgnoreAdd request failed, req: %+v, err: %+v", batchReq, err)
			return nil, err
		}
	}

	return resp, nil
}

func NotifyIgnoreDelete(ctx *gin.Context, req NotifyIgnoreDeleteReq) (*NotifyIgnoreDeleteResp, error) {
	// 如果学生ID列表为空，直接返回
	if len(req.StudentIds) == 0 {
		return &NotifyIgnoreDeleteResp{}, nil
	}

	// 将学生ID列表分批，每批最多300个
	batches := utils.ChunkArrayInt64(req.StudentIds, 300)
	resp := &NotifyIgnoreDeleteResp{}

	// 对每一批学生ID进行处理
	for _, batch := range batches {
		batchReq := NotifyIgnoreDeleteReq{
			SourceId:   req.SourceId,
			SourceType: req.SourceType,
			Operator:   req.Operator,
			StudentIds: batch,
		}

		// 发送请求
		batchResp := &NotifyIgnoreDeleteResp{}
		if err := apis.Do(ctx, batchReq, batchResp); err != nil {
			zlog.Warnf(ctx, "NotifyIgnoreDelete request failed, req: %+v, err: %+v", batchReq, err)
			return nil, err
		}
	}

	return resp, nil
}

func NotifyIgnoreQuery(ctx *gin.Context, req NotifyIgnoreQueryReq) (*NotifyIgnoreQueryResp, error) {
	// 如果学生ID列表为空，直接返回
	if len(req.StudentIds) == 0 {
		return &NotifyIgnoreQueryResp{NotifyIgnoreList: []NotifyIgnoreItem{}}, nil
	}

	// 将学生ID列表分批，每批最多300个
	batches := utils.ChunkArrayInt64(req.StudentIds, 300)
	resp := &NotifyIgnoreQueryResp{
		NotifyIgnoreList: []NotifyIgnoreItem{},
	}

	// 对每一批学生ID进行处理
	for _, batch := range batches {
		batchReq := NotifyIgnoreQueryReq{
			SourceId:   req.SourceId,
			SourceType: req.SourceType,
			StudentIds: batch,
		}

		// 发送请求
		batchResp := &NotifyIgnoreQueryResp{}
		if err := apis.Do(ctx, batchReq, batchResp); err != nil {
			zlog.Warnf(ctx, "NotifyIgnoreQuery request failed, req: %+v, err: %+v", batchReq, err)
			return nil, err
		}

		resp.NotifyIgnoreList = append(resp.NotifyIgnoreList, batchResp.NotifyIgnoreList...)
	}

	return resp, nil
}

func NotifyIgnoreFilter(ctx *gin.Context, req NotifyIgnoreFilterReq) (*NotifyIgnoreFilterResp, error) {
	// 如果学生ID列表为空，直接返回
	if len(req.StudentIds) == 0 {
		return &NotifyIgnoreFilterResp{NotifyStudentIds: []int64{}}, nil
	}

	// 将学生ID列表分批，每批最多300个
	batches := utils.ChunkArrayInt64(req.StudentIds, 300)
	resp := &NotifyIgnoreFilterResp{
		NotifyStudentIds: []int64{},
	}

	// 对每一批学生ID进行处理
	for _, batch := range batches {
		batchReq := NotifyIgnoreFilterReq{
			SourceId:     req.SourceId,
			SourceType:   req.SourceType,
			StudentIds:   batch,
			AssistantUid: req.AssistantUid,
		}

		// 发送请求
		batchResp := &NotifyIgnoreFilterResp{}
		if err := apis.Do(ctx, batchReq, batchResp); err != nil {
			zlog.Warnf(ctx, "NotifyIgnoreFilter request failed, req: %+v, err: %+v", batchReq, err)
			return nil, err
		}

		resp.NotifyStudentIds = append(resp.NotifyStudentIds, batchResp.NotifyStudentIds...)
	}

	return resp, nil
}
