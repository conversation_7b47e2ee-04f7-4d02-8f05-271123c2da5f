package touchmisgo

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	apiGetDeviceBindInfo = "/touchmisgo/wechat/device/getdevicebindinfo"
)

type GetDeviceBindInfoReq struct {
	UserId string `json:"userId" form:"userId"`
	CorpId string `json:"corpId" form:"corpId"`
}

type GetDeviceBindInfoResp struct {
	DeviceCode        string `json:"deviceCode,omitempty"`        // 设备号
	BindDisplayNumber string `json:"bindDisplayNumber,omitempty"` // 绑定手机号
}

func GetDeviceBindInfo(ctx *gin.Context, userId, corpId string) (resp *GetDeviceBindInfoResp, err error) {
	req := GetDeviceBindInfoReq{
		UserId: userId,
		CorpId: corpId,
	}
	resp = &GetDeviceBindInfoResp{}
	if err = apis.Do(ctx, req, resp); err != nil {
		zlog.Warnf(ctx, "GetDeviceBindInfo.ral fail, req:%+v, err:%s", req, err.Error())
		return
	}
	return
}
