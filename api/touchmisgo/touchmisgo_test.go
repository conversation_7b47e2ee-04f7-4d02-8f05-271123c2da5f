package touchmisgo

import (
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"testing"
)

func getCtx() *gin.Context {
	engine := gin.New()
	gin.SetMode(gin.TestMode)
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../")
	helpers.PreInit()
	helpers.InitResource(engine)
	return ctx
}

func TestGetDeviceBindInfo(t *testing.T) {
	ctx := getCtx()

	userId := "qw250324000018"
	corpId := "wwfa20b8d34dfe7546"
	bindInfo, err := GetDeviceBindInfo(ctx, userId, corpId)
	assert.Nil(t, err)
	assert.NotEmpty(t, bindInfo.BindDisplayNumber)
	fmt.Println(bindInfo)
}
