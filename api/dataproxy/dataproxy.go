package dataproxy

import (
	"assistantdeskgo/api/apis"
	"fmt"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func ChatWordTalkList(ctx *gin.Context, req AssistantTalkListReq) (rsp *ChatWordTalkListRsp, err error) {
	rsp = &ChatWordTalkListRsp{}
	if err = apis.Do(ctx, req, rsp); err != nil {
		zlog.Warnf(ctx, "ChatWordTalkList request failed, req: %d, err: %+v", req, err)

	}
	return
}

func ChatWordTalkSearch(ctx *gin.Context, req ChatWordTalkSearchReq) (rsp *ChatWordTalkSearchRsp, err error) {
	rsp = &ChatWordTalkSearchRsp{}
	if err = apis.Do(ctx, req, rsp); err != nil {
		zlog.Warnf(ctx, "ChatWordTalkSearch request failed, req: %d, err: %+v", req, err)

	}
	return
}

func GetCourseTaskTiStuData(ctx *gin.Context, req GetCourseTaskTiStuDataReq) (rsp GetCourseTaskTiStuDataRsp, err error) {
	if err = apis.Do(ctx, req, &rsp); err != nil {
		zlog.Warnf(ctx, "ChatWordTalkSearch request failed, req: %d, err: %+v", req, err)
	}
	return
}

// GetStaffInfoListByFilter 获取用户信息列表,支持检索
func GetStaffInfoListByFilter(ctx *gin.Context, req StaffInfoListReq) (rsp *StaffInfoListRsp, err error) {
	zlog.Infof(ctx, "GetStaffInfoListByFilter req:%+v", req)
	var allStaffInfo []StaffInfo
	var offset string
	var total, currentTotal int64
	var iteration = 0
	var maxIterations = 100

	for {
		iteration++
		if iteration > maxIterations {
			err = fmt.Errorf("GetStaffInfoListByFilter exceeded max iterations")
			return
		}

		// 设置当前请求的 offset
		req.Offset = offset

		// 请求数据
		var pageRsp StaffInfoListRsp
		if err = apis.Do(ctx, req, &pageRsp); err != nil {
			zlog.Warnf(ctx, "GetStaffInfoListByFilter request failed, req: %+v, err: %+v", req, err)
			return
		}
		total = pageRsp.Total
		allStaffInfo = append(allStaffInfo, pageRsp.List...)

		// 更新当前获取的总数,检查是否已经获取了所有数据
		currentTotal += int64(len(pageRsp.List))
		if currentTotal >= total {
			break
		}

		// 检查是否还有下一页
		if len(pageRsp.Offset) == 0 {
			break
		}
		offset = pageRsp.Offset
	}

	// 构造最终返回结果
	rsp = &StaffInfoListRsp{
		List:  allStaffInfo,
		Total: total,
	}
	zlog.Infof(ctx, "GetStaffInfoListByFilter rsp:%+v", rsp)
	return
}

func GetCommonListByStudentLessons(ctx *gin.Context, req LuStudentLessonsReq) (rsp CommonLuRsp, err error) {
	if err = apis.Do(ctx, req, &rsp); err != nil {
		zlog.Warnf(ctx, "GetCommonListByStudentLessons error! req:%+v, err:%+v", req, err)
		return
	}
	return
}

func GetCommonListByLessonsStudents(ctx *gin.Context, req LuLessonsStudentsReq) (rsp CommonLuRsp, err error) {
	if err = apis.Do(ctx, req, &rsp); err != nil {
		zlog.Warnf(ctx, "GetCommonListByLessonsStudents error! req:%+v, err:%+v", req, err)
		return
	}
	return
}

func GetListByCourseIdLessonIdsStudentUidsAssistantUid(ctx *gin.Context, req LuCourseLessonIdsStudentUidsAssistantUidReq) (rsp LessonStudentActionRsp, err error) {
	if err = apis.Do(ctx, req, &rsp); err != nil {
		zlog.Warnf(ctx, "GetListByCourseIdLessonIdsStudentUidsAssistantUid error! req:%+v, err:%+v", req, err)
		return
	}
	return
}

func GetPointDataByLessonIdsStudentUid(ctx *gin.Context, req LupLessonsStudentUidReq) (rsp LessonStudentPointActionRsp, err error) {
	if err = apis.Do(ctx, req, &rsp); err != nil {
		zlog.Warnf(ctx, "GetPointDataByLessonIdsStudentUid error! req:%+v, err:%+v", req, err)
		return
	}
	return
}

func GetPointDataByLessonIdsStudentUids(ctx *gin.Context, req LupLessonsStudentUidsReq) (rsp LessonStudentPointActionRsp, err error) {
	if err = apis.Do(ctx, req, &rsp); err != nil {
		zlog.Warnf(ctx, "GetPointDataByLessonIdsStudentUids error! req:%+v, err:%+v", req, err)
		return
	}
	return
}

func GetPointDataByLessonIdsStudentUidsExamType(ctx *gin.Context, req LupLessonsStudentUidsExamTypeReq) (rsp LessonStudentPointExamActionRsp, err error) {
	if err = apis.Do(ctx, req, &rsp); err != nil {
		zlog.Warnf(ctx, "GetPointDataByLessonIdsStudentUidsExamType error! req:%+v, err:%+v", req, err)
		return
	}
	return
}

func GetLpPointCommonByLessonIds(ctx *gin.Context, req LpCommonPointLessonIdsReq) (rsp LpCommonPointLessonIdsRsp, err error) {
	if err = apis.Do(ctx, req, &rsp); err != nil {
		zlog.Warnf(ctx, "GetLpPointCommonByLessonIds error! req:%+v, err:%+v", req, err)
		return
	}
	return
}

func GetLeadsAdsBaseModelByCourse(ctx *gin.Context, req GetLeadsAdsBaseModelReq) (rsp GetLeadsAdsBaseModelResp, err error) {
	if err = apis.Do(ctx, req, &rsp); err != nil {
		zlog.Warnf(ctx, "GetLeadsAdsBaseModelByCourse error! req:%+v, err:%+v", req, err)
		return
	}
	return
}
