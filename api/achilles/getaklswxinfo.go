package achilles

import (
	"assistantdeskgo/api/apis"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const GeTaklsWxInfoUrl = "/achilles/v3/origin/plum/api/getaklswxinfo"

type GeTaklsWxInfoReq struct {
	Key []string `json:"key" form:"key"`
}

type GeTaklsWxInfoRsp struct {
	InspectorQrcode map[string]InspectorQrcodeInfo `json:"inspectorQrcode"`
}

type InspectorQrcodeInfo struct {
	ScWxInfo ScWxInfo `json:"scWxInfo"`
}

type ScWxInfo struct {
	LpcUid    int64  `json:"lpcUid"`
	PersonUid int64  `json:"personUid"`
	LpcName   string `json:"lpcName"`
	NickName  string `json:"nickName"`
}

func GeTaklsWxInfo(ctx *gin.Context, courseId, studentUid int64) (result ScWxInfo, err error) {
	req := GeTaklsWxInfoReq{
		Key: []string{fmt.Sprintf("%d_%d", courseId, studentUid)},
	}
	resp := GeTaklsWxInfoRsp{}
	err = apis.Do(ctx, req, &resp)
	if err != nil {
		zlog.Warnf(ctx, "GetLeadsByUid failed, req: %+v, resp: %+v, err: %+v", req, resp, err)
		return
	}
	info, ok := resp.InspectorQrcode[fmt.Sprintf("%d_%d", courseId, studentUid)]
	if ok {
		return info.ScWxInfo, nil
	}
	return
}
