package conf

import (
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/cos"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/kafka"
	"git.zuoyebang.cc/pkg/golib/v2/redis"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/server/http"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
)

var (
	// 配置文件对应的全局变量
	API       TApi
	BasicConf TBasic
	RConf     ResourceConf
)

// 基础配置,对应config.yaml
type TBasic struct {
	Pprof  base.PprofConfig
	Log    zlog.LogConfig
	Server http.ServerConfig
	// ....业务可扩展其他简单的配置
}

// 对应 resource.yaml
type ResourceConf struct {
	Rmq      rmq.RmqConfig `yaml:"rmqv2"`
	Redis    map[string]redis.RedisConf
	Cos      map[string]cos.BucketConfig
	Elastic  map[string]ElasticClientConfig
	Mysql    map[string]base.MysqlConf
	KafkaPub map[string]kafka.ProducerConfig
}

type ApiProxy struct {
	Domain       string   `yaml:"domain"`
	Entries      []string `yaml:"entries"`
	EntriesNoIps []string `yaml:"entriesnoips"`
}

type ElasticClientConfig struct {
	base.ElasticClientConfig `yaml:",inline"`
	Version                  int `yaml:"version"`
}

// 对应 api.yaml
type TApi struct {
	Demo base.ApiClient

	Mesh          base.ApiClient `yaml:"mesh"`
	UserProfile   base.ApiClient `yaml:"userprofile"`
	Passport      base.ApiClient `yaml:"passport"`
	AssistantDesk base.ApiClient `yaml:"assistantdesk"`

	ZbCoreDau    base.ApiClient `yaml:"zbcore_dau"`
	ZbCoreDal    base.ApiClient `yaml:"zbcore_dal"`
	ZbCoreDas    base.ApiClient `yaml:"zbcore_das"`
	ZbCoreDat    base.ApiClient `yaml:"zbcore_dat"`
	CourseSearch base.ApiClient `yaml:"coursesearch"`
	Tower        base.ApiClient `yaml:"tower"`

	Galaxy            base.ApiClient `yaml:"galaxy"`
	Su                base.ApiClient `yaml:"su"`
	Accelerate        base.ApiClient `yaml:"accelerate"`
	Moat              base.ApiClient `yaml:"moat"`
	KunPeng           base.ApiClient `yaml:"kunpeng"`
	Mercury           base.ApiClient `yaml:"mercury"`
	Plum              base.ApiClient `yaml:"plum"`
	Muse              base.ApiClient `yaml:"muse"`
	TouchMis          base.ApiClient `yaml:"touchmis"`
	OfficeServer      base.ApiClient `yaml:"officeServer"`
	LpcMsg            base.ApiClient `yaml:"lpcmsg"`
	InfoMsg           base.ApiClient `yaml:"info_msg"`
	ClassMe           base.ApiClient `yaml:"classme"`
	FwyyEvaluate      base.ApiClient `yaml:"fwyy-evaluate"`
	AiTurbo           base.ApiClient `yaml:"aiturbo"`
	ASR               base.ApiClient `yaml:"asr"`
	ASR2              base.ApiClient `yaml:"asr2"`
	KpStaff           base.ApiClient `yaml:"kpStaff"`
	AssistantcourseGo base.ApiClient `yaml:"assistantcoursego"`
	Exercise          base.ApiClient `yaml:"exercise"`
	DataProxy         base.ApiClient `yaml:"dataproxy"`
	Duxuesc           base.ApiClient `yaml:"duxuesc"`
	Allocate          base.ApiClient `yaml:"allocate"`
	ExamCore          base.ApiClient `yaml:"examcore"`
	CourseBase        base.ApiClient `yaml:"coursebase"`
	TouchmisGo        base.ApiClient `yaml:"touchmisgo"`
	LongLink          base.ApiClient `yaml:"longlink"`
	ZbTikuApi         base.ApiClient `json:"zbtikuapi"`
	Delayer           base.ApiClient `yaml:"delayer"`
	UCloud            base.ApiClient `yaml:"ucloud"`
	CoursetransGo     base.ApiClient `yaml:"coursetransgo"`
	Agg               base.ApiClient `yaml:"agg"`
	Eduprobe          base.ApiClient `yaml:"eduprobe"`
	AssistantAi       base.ApiClient `yaml:"assistantai"`
	Laxinmis          base.ApiClient `yaml:"laxinmis"`
	MisCourse         base.ApiClient `yaml:"miscourse"`
	JxReport          base.ApiClient `yaml:"jxreport"`
	AchillesV3Server  base.ApiClient `yaml:"achilles-v3-server"`
	ArkGo             base.ApiClient `yaml:"arkgo"`
}

type TCustom struct {
	Mesh     Mesh                `yaml:"mesh"`
	PreClass PreClass            `yaml:"preClass"`
	ApiProxy map[string]ApiProxy `yaml:"proxyConfig"`
}

type Mesh struct {
	AppId            string  `yaml:"appId"`
	ModuleId         string  `yaml:"moduleId"`
	FudaoProductLine string  `yaml:"fudaoProductLine"`
	LaxinProductLine string  `yaml:"laxinProductLine"`
	GroupId          []int64 `yaml:"groupId"`
}

type PreClass struct {
	AiAutoConsumeQps int `yaml:"aiAutoConsumeQps"`
}

var Custom TCustom

func InitConf() {
	// 加载通用基础配置（必须）
	env.LoadConf("config.yaml", env.SubConfMount, &BasicConf)
	env.LoadConf("resource.yaml", env.SubConfMount, &RConf)
	env.LoadConf("api.yaml", env.SubConfMount, &API)
	env.LoadConf("custom.yaml", env.SubConfMount, &Custom)
}
