cos:
  bos:
    bucket: test-image
    cloud: baidu
    cnameEnabled: true
    directory: ""
    file_prefix: fudao_
    filesize_limit: ***********
    region: https://testimg.zuoyebang.cc
    secret_id: 80054b2251d24cb98eda994c93426d7d
    secret_key: 2dfba51842f2493d82339574941a5df2
  bos1:
    bucket: charge
    cloud: baidu
    cnameEnabled: true
    directory: ""
    file_prefix: fudao_
    filesize_limit: ***********
    region: https://test-charge.bj.bcebos.com
    secret_id: 80054b2251d24cb98eda994c93426d7d
    secret_key: 2dfba51842f2493d82339574941a5df2
  cos:
    bucket: zyb-image
    app_id: 1253445850
    secret_id: AKIDMUifMoX1kFTbx3uUnqc79qHSNo0wudOu
    secret_key: OW1OYS6V4QXZNFuKbfWAbNuBAsML4V9X
    region: ap-beijing
    picture_region: picbj
    filesize_limit: 2097152
    thumbnail: 1
    directory: ""
    file_prefix: fudao_
    cloud: tencent
    cdn: https://testimg.zuoyebang.cc

elastic:
  # es7.10
  workbench:
    service: workbench
    #    addr: http://*************:9200
    addr: https://127.0.0.1:9200
    # 扩展属性
    version: 7
    username: elastic
    password: ivDZ-U4DpexsJ*+a_Yoe
    healthCheck: false
    # 线上环境禁止DEBUG日志
    # debugMsgLen: -1
    # infoMsgLen: -1
    gzip: true

  dataengine:
    service: dataengine
    addr: http://*************:9200
    # 扩展属性
    version: 7
    username: ship_test
    password: ship_test
    debugMsgLen: -1
    infoMsgLen: 1024
    # 线上环境禁止DEBUG日志
    # debugMsgLen: -1
    # infoMsgLen: -1
    gzip: true

  performance:
    # 压测es集群
    service: performance
    addr: http://*************:9200
    # 扩展属性
    version: 7
    username: ship_test
    password: ship_test
    healthCheck: false
    # 线上环境禁止DEBUG日志
    # debugMsgLen: -1
    # infoMsgLen: -1
    gzip: true

  # es6.7
  lpc:
    service: lpc
    addr: http://lpces-f.zuoyebang.cc
    version: 6
    username: lpcuser
    password: lpcuser
    healthCheck: false
    # 线上环境禁止DEBUG日志
    # debugMsgLen: -1
    # infoMsgLen: -1
    gzip: true

mysql:
  bzr:
    addr: *************:3306
    user: homework
    password: homework
    database: homework_fudao
    maxIdleTime: 300s
    maxidleconns: 50
    maxopenconns: 50
    readTimeOut: 3s
    service: bzr
    writeTimeOut: 3s
    connMaxLifeTime: 3600s
    connTimeOut: 1500ms
  duxuesc:
    addr: ************:3306
    user: homework
    password: homework
    database: homework_zhibo_duxuesc
    maxIdleTime: 300s
    maxidleconns: 50
    maxopenconns: 50
    readTimeOut: 3s
    service: duxuesc
    writeTimeOut: 3s
    connMaxLifeTime: 3600s
    connTimeOut: 1500ms
redis:
  assistantdeskgo:
    service: assistantdeskgo
    addr: redis-qatest-svc.redis:6379
    maxIdle: 10
    maxActive: 200
    idleTimeout: 3s
    connTimeOut: 1s
    readTimeOut: 1s
    writeTimeOut: 1s
rmqv2:
  consumer:
    - service: "autoCallConsumer"
      nameserver: mq-base-svc.mq:9876
      topic: "support_assistantdesk_fwyy-zb-core"
      group: "assistantdeskgo_autocall"
      tags:
        - "217308"
      orderly: false
      retry: 3
      retry_interval: 10ms
      delay: 1s
    - service: newLeadNotice
      nameserver: mq-base-svc.mq:9876
      topic: lpc_allocate_fwyy-zb-core
      group: assistantdeskgo_newleadnotice
      tags:
        - "280022"
        - "280023"
      orderly: false
      retry: 3
      retry_interval: 10ms
      delay: 1s
    - service: wxCallRecordConsumer
      nameserver: mq-base-svc.mq:9876
      topic: kunpeng-zb-kunpeng
      group: assistantdeskgo_kunpeng_wxcallrecord
      tags:
        - "182007"
      orderly: false
      retry: 3
      retry_interval: 10ms
      delay: 1s
    - service: KP182001-lpc
      nameserver: mq-base-svc.mq:9876
      topic: kunpeng-zb-kunpeng
      group: assistantdeskgo_kunpeng_182001_lpc
      tags:
        - "182001"
      orderly: false
      retry: 3
      retry_interval: 10ms
      delay: 1s
    - service: KP182001
      nameserver: mq-base-svc.mq:9876
      topic: kunpeng-zb-kpteacher
      group: assistantdeskgo_kunpeng_182001
      tags:
        - "182001"
      orderly: false
      retry: 3
      retry_interval: 10ms
      delay: 1s
    - broadcast: false
      group: assistantdeskgo_self_capture
      nameserver: mq-base-svc.mq:9876
      orderly: false
      retry: 3
      service: self_consume
      tags:
        - 214200
      topic: support_assistantdeskgo_fwyy-zb-core
    - service: laxinTagFixImport
      nameserver: mq-base-svc.mq:9876
      topic: support_assistantdeskgo_fwyy-zb-core
      group: assistantdeskgo_laxin_tag_fix_import
      tags:
        - 214202
      orderly: true
      retry: 3
      retry_interval: 10ms
      delay: 1s
    - service: sceneTouchPrepare
      nameserver: mq-base-svc.mq:9876
      topic: support_assistantdeskgo_fwyy-zb-core
      group: support_assistantdeskgo_scenetouch_prepare
      tags:
        - 214204
      orderly: true
      retry: 3
      retry_interval: 10ms
      delay: 1s
    - service: aiAutoTag
      nameserver: mq-jiaoxue-svc.mq:9876
      topic: edu-aigc_insight_trustee
      group: assistantdeskgo_sop_ai_banxue_tag
      tags:
        - "aiGenerateTag"
      orderly: true
      retry: 3
      retry_interval: 10ms
      delay: 1s
    - service: touchLpcMsg
      nameserver: mq-fwyy-svc.mq:9876
      topic: support_muse_fwyy-zb-lpcmsg
      group: support_assistantdeskgo_muselpcmsg_touch
      tags:
        - "271308"
      orderly: true
      retry: 3
      retry_interval: 10ms
      delay: 1s
    - service: onceTaskConsumer
      nameserver: mq-fwyy-svc.mq:9876
      topic: support_delayer_delay-message
      group: support_assistantdeskgo_once_task
      tags:
        - "218108"
      orderly: false
      retry: 10
      retry_interval: 300ms
  producer:
    - nameserver: mq-base-svc.mq:9876
      retry: 1
      service: self_product
      topic: support_assistantdeskgo_fwyy-zb-core
    - nameserver: mq-base-svc.mq:9876
    - service: "touchmisgoSopAIConfProducer"
      nameserver: "ship-rocketmq-aictrl-svc.mq:9876"
      topic: "support_touchmisgo_fwyy-zb-touchnotify"
      retry: 3
      timeout: 1000ms
    - nameserver: mq-fwyy-svc.mq:9876
      retry: 1
      service: sceneTouchPrepare
      topic: support_assistantdeskgo_fwyy-zb-core

kafkapub:
  metrics:
    service: fwyy-data-assistant-metricscommon
    addr: "*************:9092,*************:9092,************:9092"
    version: 2.0.0
