package publicsea

import (
	"assistantdeskgo/api/allocate"
	"assistantdeskgo/api/dau"
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtorelation/dtopublicsea"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	"assistantdeskgo/service/relation/datalayer"
	"assistantdeskgo/service/relation/fields"
	"assistantdeskgo/service/relation/metrics"
	"assistantdeskgo/utils"
	"fmt"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"time"
)

func Retrieve(ctx *gin.Context, req dtopublicsea.RetrieveReq) (resp dtopublicsea.RetrieveResp, err error) {
	info, err := middleware.GetSelectDeviceInfo(ctx)
	if err != nil {
		return
	}
	studentUid, courseId, err := utils.SplitBusinessKey(req.ClueId)
	if err != nil {
		return
	}

	// 获取回捞用户
	studentsMap, err := dau.GetStudents(ctx, []int64{studentUid}, []string{"studentUid", "grade"})
	if err != nil {
		return
	}
	studentInfo, ok := studentsMap[studentUid]
	if !ok {
		err = components.ErrorGetStudentInfo
		return
	}

	// check 回捞条件
	opLogMap := datalayer.GetPublicSeaOpLogMapByClueIdsDeviceId(ctx, []string{req.ClueId}, info.DeviceUid)
	opLog, ok := opLogMap[req.ClueId]
	if !ok {
		err = components.ErrorCanNotRetrieve
		return
	}
	if opLog.LastAccessTime < time.Now().Add(-2*time.Hour).Unix() {
		err = components.ErrorCanNotRetrieve
		return
	}

	// 先lock
	lockKey := fmt.Sprintf(defines.LockKeyRelationRetrieve, req.ClueId)
	lock, err := helpers.RedisClient.SetNxByEX(ctx, lockKey, 1, defines.LockKeyRelationRetrieveExSecond)
	if err != nil {
		return
	}
	if !lock {
		err = components.ErrorHasBeenLocked.Sprintf("当前线索已经被其他用户回捞")
		return
	}
	defer func() {
		_, _ = helpers.RedisClient.Del(ctx, lockKey)
	}()

	subscribeInfo := map[string]int{
		"leadsOrigin": allocate.LeadsOriginPC, // 下游写死的map key，我也不知道为啥这么传
	}
	subscribeInfoStr, _ := jsoniter.MarshalToString(subscribeInfo)
	retrieveReq := allocate.NoCourseLeadsEntranceReq{
		StudentUid:    studentUid,
		CourseId:      courseId,
		Grade:         int64(studentInfo.Grade),
		ActivityId:    fields.PublicSeaRetrieveType,
		DeviceUid:     info.DeviceUid,
		SubscribeInfo: subscribeInfoStr,
	}
	allResp, err := allocate.NoCourseLeadsEntrance(ctx, retrieveReq)
	if err != nil {
		return
	}
	resp.RetrieveStatus = allResp.Ret
	if allResp.Ret {
		metrics.LogRetrieveSuccess(ctx, info.DeviceUid, studentUid)
	}
	return
}
