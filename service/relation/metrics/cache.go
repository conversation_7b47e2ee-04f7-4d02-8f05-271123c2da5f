package metrics

import (
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	lockOnlineDeviceUid = "deskgo:lock:ps:online_device_uid_%d" // %d=资产uid
)

func LogPublicSeaOnlineDeviceUid(ctx *gin.Context, deviceUid int64) {
	if deviceUid == 0 {
		return
	}
	ok, err := helpers.RedisClient.SetNxByEX(ctx, fmt.Sprintf(lockOnlineDeviceUid, deviceUid), 1, 60)
	if err != nil {
		zlog.Warnf(ctx, "LogPublicSeaOnlineDeviceUid.lock key:%s, err:%s", lockOnlineDeviceUid, err.Error())
		return
	}
	if ok {
		zlog.Infof(ctx, "[公海指标_在线资产]当前在线资产:%d", deviceUid)
	}
}

func LogRetrieveSuccess(ctx *gin.Context, deviceUid, studentUid int64) {
	zlog.Infof(ctx, "[公海指标_公海回捞]回捞成功,资产[%d],学生[%d]", deviceUid, studentUid)
}
