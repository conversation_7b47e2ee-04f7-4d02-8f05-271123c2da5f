package notice

import (
	"assistantdeskgo/api/dataproxy"
	"assistantdeskgo/api/userprofile"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtonotice"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	"assistantdeskgo/models/notice"
	"assistantdeskgo/utils"
	"sort"
	"strings"
	"sync"
	"time"

	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// SaveOrUpdateNotice 新建或更新通知
func SaveOrUpdateNotice(ctx *gin.Context, param dtonotice.SystemNoticeSaveParam) (result *dtonotice.CommonRsp, checkMsg string, err error) {
	receiverScope := dtonotice.ReceiverScopeParam{
		Scope:      param.Scope,
		ScopeGroup: param.ScopeGroup,
		ScopeUID:   param.ScopeUID,
	}
	checkMsg = checkGroupIdAndUid(ctx, receiverScope)
	if checkMsg != "" {
		return
	}

	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return
	}

	if param.Id == 0 {
		insertNotice := notice.SystemNotice{
			Title:                 param.Title,
			CreatorUID:            int64(userInfo.UserId),
			ClassID:               param.ClassID,
			Status:                param.Status, //这里的status可能为编辑或者发布，根据前端传参
			Scope:                 param.Scope,
			ScopeGroup:            param.ScopeGroup,
			ScopeUID:              param.ScopeUID,
			ProfilePhoto:          param.ProfilePhoto,
			Content:               param.Content,
			VideoAddr:             param.VideoAddr,
			Abstract:              param.Abstract,
			CommentFlag:           param.CommentFlag,
			MockReadNum:           param.MockReadNum,
			MockContentLikeNum:    param.MockContentLikeNum,
			MockVideoLikeNum:      param.MockVideoLikeNum,
			MockContentDisLikeNum: param.MockContentDisLikeNum,
			MockVideoDisLikeNum:   param.MockVideoDisLikeNum,
			MockCommentNum:        param.MockCommentNum,
			CreateTime:            time.Now().Unix(),
			UpdateTime:            time.Now().Unix(),
		}

		if param.Status == defines.SystemNoticeStatusPublish {
			insertNotice.PublisherUID = int64(userInfo.UserId)
			insertNotice.PublishTime = time.Now().Unix()
		}

		var insertId int64
		insertId, err = notice.SystemNoticeDao.Insert(ctx, insertNotice, nil)
		if err != nil {
			return
		}

		// 发布时保存SystemNoticeUser，这样对应人员才能看到通知
		if param.Status == defines.SystemNoticeStatusPublish {
			go func() {
				err := fwyyutils.RunWithRetry(func(idx int) error {
					return saveNoticeReceiver(ctx, insertId, receiverScope)
				})
				if err != nil {
					zlog.Errorf(ctx, "saveNoticeReceiver failed, noticeId: %d, err:%+v", insertNotice.Id, err)
				}
			}()
		}
	} else {
		updateNotice := map[string]interface{}{
			"title":                    param.Title,
			"status":                   param.Status, //这里的status可能为编辑或者发布，根据前端传参
			"scope":                    param.Scope,
			"scope_group":              param.ScopeGroup,
			"scope_uid":                param.ScopeUID,
			"profile_photo":            param.ProfilePhoto,
			"content":                  param.Content,
			"abstract":                 param.Abstract,
			"mock_read_num":            param.MockReadNum,
			"mock_content_like_num":    param.MockContentLikeNum,
			"mock_video_like_num":      param.MockVideoLikeNum,
			"mock_content_dislike_num": param.MockContentDisLikeNum,
			"mock_video_dislike_num":   param.MockVideoDisLikeNum,
			"mock_comment_num":         param.MockCommentNum,
		}

		if param.Status == defines.SystemNoticeStatusPublish {
			updateNotice["publisher_uid"] = int64(userInfo.UserId)
			updateNotice["publish_time"] = time.Now().Unix()
		}

		err = notice.SystemNoticeDao.UpdateById(ctx, param.Id, updateNotice, nil)
		if err != nil {
			return
		}

		if param.Status == defines.SystemNoticeStatusPublish {
			// 新增或删除人员范围时，要同步更新SystemNoticeUser，这样对应人员才能看到或隐藏相应的通知
			go func() {
				err := fwyyutils.RunWithRetry(func(idx int) error {
					return updateNoticeReceiver(ctx, param.Id, receiverScope)
				})
				if err != nil {
					zlog.Errorf(ctx, "updateNoticeReceiver failed, noticeId: %d, err:%+v", param.Id, err)
				}
			}()
		}
	}

	return
}

// GetNoticeList 获取通知列表
func GetNoticeList(ctx *gin.Context, param dtonotice.SystemNoticeListParam) (rsp dtonotice.NoticeListRsp, err error) {
	list, count, err := notice.SystemNoticeDao.Page(ctx, param, nil)
	if err != nil {
		return
	}

	var resultList []dtonotice.SystemNotice
	for _, n := range list {
		statistic, err := getManagerNoticeStatistic(ctx, n.Id)
		if err != nil {
			return rsp, err
		}

		userinfo, err := getUserInfoByUid(ctx, n.PublisherUID)
		if err != nil {
			return rsp, err
		}

		// 如果有多个组织，则用分号分隔
		var groupNames []string
		for _, org := range userinfo.Organization {
			groupNames = append(groupNames, org.GroupName)
		}

		systemNoticeList := dtonotice.SystemNotice{
			Id:             n.Id,
			Title:          n.Title,
			PublisherUid:   n.PublisherUID,
			PublisherName:  userinfo.StaffName,
			PublisherGroup: strings.Join(groupNames, ";"),
			ClassId:        n.ClassID,
			Status:         n.Status,
			PublishTime:    n.PublishTime,
			UpdateTime:     n.UpdateTime,
			Scope:          n.Scope,
			Statistics:     statistic,
		}

		resultList = append(resultList, systemNoticeList)
	}
	rsp.List = resultList
	rsp.Total = count
	return
}

// GetNotice 获取通知详情
func GetNotice(ctx *gin.Context, noticeId int64) (systemNotice *notice.SystemNotice, err error) {
	return notice.SystemNoticeDao.GetById(ctx, noticeId, nil)
}

// ManagerNoticeDetail 获取通知详情
func ManagerNoticeDetail(ctx *gin.Context, noticeId int64) (systemNotice *dtonotice.ManagerNoticeDetailRsp, err error) {
	// 1. 获取通知基本信息
	noticeInfo, err := notice.SystemNoticeDao.GetById(ctx, noticeId, nil)
	if err != nil {
		return nil, err
	}

	// 2. 获取发布者信息
	userinfo, err := getUserInfoByUid(ctx, noticeInfo.PublisherUID)
	if err != nil {
		return nil, err
	}

	// 3. 获取统计信息
	statistics, err := getManagerNoticeStatistic(ctx, noticeId)
	if err != nil {
		return nil, err
	}

	// 4. 获取评论详情
	comments, err := getCommentsByNoticeId(ctx, noticeId, true)
	if err != nil {
		return nil, err
	}

	// 5. 组装返回结果
	systemNotice = &dtonotice.ManagerNoticeDetailRsp{
		Id:            noticeInfo.Id,
		ClassId:       noticeInfo.ClassID,
		Title:         noticeInfo.Title,
		PublisherName: userinfo.StaffName,
		PublishTime:   noticeInfo.PublishTime,
		FinishTime:    noticeInfo.UpdateTime,
		Content:       noticeInfo.Content,
		VideoAddr:     noticeInfo.VideoAddr,
		CommentDetail: comments,
		Statistics:    statistics,
	}
	return
}

// DeleteNotice 删除通知
func DeleteNotice(ctx *gin.Context, noticeId int64) (result *dtonotice.CommonRsp, err error) {
	// 开启事务
	tx := helpers.MysqlClientFuDao.Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = notice.SystemNoticeDao.Delete(ctx, map[string]interface{}{"id": noticeId}, tx)
	if err != nil {
		return
	}

	// 删除对应的SystemNoticeUser
	err = deleteNoticeReceiver(ctx, tx, noticeId)
	if err != nil {
		return
	}

	return
}

// ChangeNoticeStatus 更改通知状态：编辑、发布、完成
func ChangeNoticeStatus(ctx *gin.Context, noticeId int64, status int8) (result *dtonotice.CommonRsp, err error) {
	if status == defines.SystemNoticeStatusFinish {
		err = notice.SystemNoticeDao.UpdateById(ctx, noticeId, map[string]interface{}{"status": defines.SystemNoticeStatusFinish}, nil)
		if err != nil {
			return
		}
	}

	return
}

// GetNoticeExpectedNum 获取期望通知人数
func GetNoticeExpectedNum(ctx *gin.Context, rs dtonotice.ReceiverScopeParam) (result *dtonotice.CountRsp, err error) {
	uids, err := getUidsByScope(ctx, rs)
	if err != nil {
		return nil, err
	}
	result = &dtonotice.CountRsp{Count: len(uids)}
	return
}

// GetNoticeExpectedUserList 获取期望通知人员范围列表
func GetNoticeExpectedUserList(ctx *gin.Context, param dtonotice.ReceiverFilterParam) (rsp dtonotice.NoticeUserInfoListRsp, err error) {
	// 1. 获取通知范围内的所有uid
	result, err := getUidsByScope(ctx, dtonotice.ReceiverScopeParam{
		Scope:      param.Scope,
		ScopeGroup: param.ScopeGroup,
		ScopeUID:   param.ScopeUID,
	})
	if err != nil {
		return
	}

	// 如果过滤条件有uid，在这里先筛选
	var uids []int64 = filterByUid(cast.ToInt64(param.Uid), result)
	if len(uids) == 0 {
		return
	}

	return getNoticeReceiverList(ctx, uids, param)
}

// InformList 获取发送列表
func InformList(ctx *gin.Context, param dtonotice.ReceiverFilterParam) (rsp dtonotice.NoticeUserInfoListRsp, err error) {
	return processNoticeUserList(
		ctx,
		param,
		func() ([]int64, error) {
			return notice.SystemNoticeUserDao.GetSendUids(ctx, param, nil)
		},
		fillSendFields,
		sortBySendTime,
	)
}

// Readlist 获取阅读列表
func Readlist(ctx *gin.Context, param dtonotice.ReceiverFilterParam) (rsp dtonotice.NoticeUserInfoListRsp, err error) {
	return processNoticeUserList(
		ctx,
		param,
		func() ([]int64, error) {
			return notice.SystemNoticeUserDao.GetReadUids(ctx, param, nil)
		},
		fillReadFields,
		sortByReadTime,
	)
}

// UnReadlist 获取未阅读列表
func UnReadlist(ctx *gin.Context, param dtonotice.ReceiverFilterParam) (rsp dtonotice.NoticeUserInfoListRsp, err error) {
	return processNoticeUserList(
		ctx,
		param,
		func() ([]int64, error) {
			return notice.SystemNoticeUserDao.GetUnReadUids(ctx, param, nil)
		},
		fillUnReadFields,
		sortBySendTime,
	)
}

// Feedbacklist 获取点赞列表
func Feedbacklist(ctx *gin.Context, param dtonotice.ReceiverFilterParam) (rsp dtonotice.NoticeUserInfoListRsp, err error) {
	return processNoticeUserList(
		ctx,
		param,
		func() ([]int64, error) {
			return notice.SystemNoticeUserDao.GetFeedbackUids(ctx, param, nil)
		},
		fillFeedbackFields,
		sortByFeedbackTime,
	)
}

// 校验groupId和uid
func checkGroupIdAndUid(ctx *gin.Context, receiverScope dtonotice.ReceiverScopeParam) (msg string) {
	if receiverScope.Scope != defines.SystemNoticeScopeFilter {
		return
	}

	var invalidGroups []int64
	var invalidUids []int64
	var errMsgs []string

	// 校验组织ID
	if len(receiverScope.ScopeGroup) > 0 {
		split := strings.Split(receiverScope.ScopeGroup, ",")
		for _, str := range split {
			groupId := cast.ToInt64(strings.TrimSpace(str))
			if groupId <= 0 {
				invalidGroups = append(invalidGroups, groupId)
				continue
			}

			rsp, err := dataproxy.GetStaffInfoListByFilter(ctx, dataproxy.StaffInfoListReq{
				GroupId:           []int64{groupId},
				NeedGroupChildren: 1,
			})
			if err != nil || rsp.Total == 0 {
				invalidGroups = append(invalidGroups, groupId)
			}
		}
	}

	// 校验用户ID
	if len(receiverScope.ScopeUID) > 0 {
		split := strings.Split(receiverScope.ScopeUID, ",")
		for _, str := range split {
			uid := cast.ToInt64(strings.TrimSpace(str))
			if uid <= 0 {
				invalidUids = append(invalidUids, uid)
				continue
			}

			rsp, err := dataproxy.GetStaffInfoListByFilter(ctx, dataproxy.StaffInfoListReq{
				StaffUid: []int64{uid},
			})
			if err != nil || rsp.Total == 0 {
				invalidUids = append(invalidUids, uid)
			}
		}
	}

	// 整理所有错误信息，并返回err
	if len(invalidGroups) > 0 || len(invalidUids) > 0 || len(errMsgs) > 0 {
		errorMsg := "您输入的部门id/员工id有误，请检查以下id并重新输入："

		if len(invalidGroups) > 0 {
			var groupIds []string
			for _, id := range invalidGroups {
				groupIds = append(groupIds, cast.ToString(id))
			}
			errorMsg += "\n部门id：" + strings.Join(groupIds, "，")
		}

		if len(invalidUids) > 0 {
			var userIds []string
			for _, id := range invalidUids {
				userIds = append(userIds, cast.ToString(id))
			}
			errorMsg += "\n员工id：" + strings.Join(userIds, "，")
		}

		return errorMsg
	}

	return ""
}

// 定义填充函数类型
type FillUserFieldsFunc func(ctx *gin.Context, userList []dtonotice.NoticeUserInfo, param dtonotice.ReceiverFilterParam, uids []int64) error

// 定义排序函数类型
type SortUserListFunc func(userList []dtonotice.NoticeUserInfo)

// 根据不同的类型，获取不同的用户信息
func processNoticeUserList(ctx *gin.Context, param dtonotice.ReceiverFilterParam,
	getUidsFunc func() ([]int64, error),
	fillFieldsFunc FillUserFieldsFunc,
	sortFunc SortUserListFunc,
) (rsp dtonotice.NoticeUserInfoListRsp, err error) {
	// 1. 获取用户ID列表
	result, err := getUidsFunc()
	if err != nil {
		zlog.Errorf(ctx, "get uids failed, err: %v", err)
		return
	}

	// 2. 过滤用户
	uids := filterByUid(cast.ToInt64(param.Uid), result)
	if len(uids) == 0 {
		return
	}

	// 3. 获取用户列表
	rsp, err = getNoticeReceiverList(ctx, uids, param)
	if err != nil {
		zlog.Errorf(ctx, "get notice receiver list failed, err: %v", err)
		return
	}

	// 4. 用户列表填充其他信息
	if err = fillFieldsFunc(ctx, rsp.List, param, uids); err != nil {
		zlog.Errorf(ctx, "fill user fields failed, err: %v", err)
		return
	}

	// 5. 排序处理
	if sortFunc != nil {
		sortFunc(rsp.List)
	}

	// 6. 分页处理
	rsp.List = processPage(rsp.List, param)

	return
}

// 各种填充函数的实现
func fillSendFields(ctx *gin.Context, userList []dtonotice.NoticeUserInfo, param dtonotice.ReceiverFilterParam, uids []int64) error {
	// 1. 获取发送信息
	noticeUsers, err := notice.SystemNoticeUserDao.GetByNoticeIdAndUids(ctx, param.NoticeId, uids, nil)
	if err != nil {
		return err
	}

	// 2. 构建映射
	userMap := make(map[int64]notice.SystemNoticeUser)
	for _, nu := range noticeUsers {
		userMap[nu.Uid] = nu
	}

	// 3. 填充信息
	for i := range userList {
		if nu, exists := userMap[userList[i].Uid]; exists {
			userList[i].SendTime = nu.CreateTime
		}
	}
	return nil
}

func fillReadFields(ctx *gin.Context, userList []dtonotice.NoticeUserInfo, param dtonotice.ReceiverFilterParam, uids []int64) error {
	// 1. 获取阅读信息
	conds := map[string]interface{}{"notice_id": param.NoticeId}
	if param.Type == defines.NoticeContentType {
		conds["is_content_read"] = true
	} else if param.Type == defines.NoticeVideoType {
		conds["is_video_read"] = true
	}
	noticeUsers, err := notice.SystemNoticeUserDao.ListByUids(ctx, conds, uids, nil)
	if err != nil {
		return err
	}

	// 2. 构建映射
	userMap := make(map[int64]notice.SystemNoticeUser)
	for _, nu := range noticeUsers {
		userMap[nu.Uid] = nu
	}

	// 3. 填充信息
	for i := range userList {
		if nu, exists := userMap[userList[i].Uid]; exists {
			userList[i].ReadTime = nu.LastReadTime
			if param.Type == defines.NoticeContentType {
				userList[i].BrowseDuration = nu.ContentReadTime
				userList[i].BrowseDurationFormat = utils.FormatDuration(nu.ContentReadTime)
			} else if param.Type == defines.NoticeVideoType {
				userList[i].BrowseDuration = nu.VideoReadTime
				userList[i].BrowseDurationFormat = utils.FormatDuration(nu.VideoReadTime)
			}
		}
	}
	return nil
}

func fillUnReadFields(ctx *gin.Context, userList []dtonotice.NoticeUserInfo, param dtonotice.ReceiverFilterParam, uids []int64) error {
	// 1. 获取阅读信息
	conds := map[string]interface{}{"notice_id": param.NoticeId}
	if param.Type == defines.NoticeContentType {
		conds["is_content_read"] = false
	} else if param.Type == defines.NoticeVideoType {
		conds["is_video_read"] = false
	}
	noticeUsers, err := notice.SystemNoticeUserDao.ListByUids(ctx, conds, uids, nil)
	if err != nil {
		return err
	}

	// 2. 构建映射
	userMap := make(map[int64]notice.SystemNoticeUser)
	for _, nu := range noticeUsers {
		userMap[nu.Uid] = nu
	}

	// 3. 填充信息
	for i := range userList {
		if nu, exists := userMap[userList[i].Uid]; exists {
			userList[i].SendTime = nu.CreateTime
		}
	}
	return nil
}

func fillFeedbackFields(ctx *gin.Context, userList []dtonotice.NoticeUserInfo, param dtonotice.ReceiverFilterParam, uids []int64) error {
	// 1. 获取点赞信息
	conds := map[string]interface{}{
		"notice_id":     param.NoticeId,
		"type":          param.Type,
		"feedback_type": param.FeedbackType,
	}
	likes, err := notice.SystemNoticeUserLikeDao.ListByUids(ctx, conds, uids, nil)
	if err != nil {
		return err
	}
	likeMap := make(map[int64]notice.SystemNoticeUserLike)
	for _, like := range likes {
		likeMap[like.Uid] = like
	}

	// 1. 获取阅读信息
	conds = map[string]interface{}{"notice_id": param.NoticeId}
	if param.Type == defines.NoticeContentType {
		conds["is_content_read"] = true
	} else if param.Type == defines.NoticeVideoType {
		conds["is_video_read"] = true
	}
	noticeUsers, err := notice.SystemNoticeUserDao.ListByUids(ctx, conds, uids, nil)
	if err != nil {
		return err
	}

	userMap := make(map[int64]notice.SystemNoticeUser)
	for _, nu := range noticeUsers {
		userMap[nu.Uid] = nu
	}

	// 3. 填充信息
	for i := range userList {
		if like, exists := likeMap[userList[i].Uid]; exists {
			userList[i].FeedbackTime = like.FeedbackTime
			userList[i].FeedbackContent = like.FeedbackContent
		}
		if nu, exists := userMap[userList[i].Uid]; exists {
			if param.Type == defines.NoticeContentType {
				userList[i].BrowseDuration = nu.ContentReadTime
				userList[i].BrowseDurationFormat = utils.FormatDuration(nu.ContentReadTime)
			} else if param.Type == defines.NoticeVideoType {
				userList[i].BrowseDuration = nu.VideoReadTime
				userList[i].BrowseDurationFormat = utils.FormatDuration(nu.VideoReadTime)
			}
		}
	}
	return nil
}

// 按照阅读时间排序
func sortByReadTime(userList []dtonotice.NoticeUserInfo) {
	sort.Slice(userList, func(i, j int) bool {
		return userList[i].ReadTime > userList[j].ReadTime
	})
}

// 按照发送时间排序
func sortBySendTime(userList []dtonotice.NoticeUserInfo) {
	sort.Slice(userList, func(i, j int) bool {
		return userList[i].SendTime > userList[j].SendTime
	})
}

// 按照反馈时间排序
func sortByFeedbackTime(userList []dtonotice.NoticeUserInfo) {
	sort.Slice(userList, func(i, j int) bool {
		return userList[i].FeedbackTime > userList[j].FeedbackTime
	})
}

func filterByUid(uid int64, result []int64) []int64 {
	uids := make([]int64, 0)

	// 如果输入为nil,直接返回空切片的指针
	if result == nil {
		return uids
	}

	for _, item := range result {
		if uid > 0 {
			if item == uid {
				uids = append(uids, uid)
				break
			}
		} else {
			uids = append(uids, item)
		}
	}
	return uids
}

func getNoticeReceiverList(ctx *gin.Context, uids []int64, param dtonotice.ReceiverFilterParam) (rsp dtonotice.NoticeUserInfoListRsp, err error) {
	// 1. 获取枚举配置信息
	confInfo, err := userprofile.GetGeneralConf(ctx)
	if err != nil {
		zlog.Errorf(ctx, "get general conf failed, err: %v", err)
		return
	}

	// 2. 分批获取用户信息
	allStaffInfo, err := batchGetStaffInfo(ctx, uids, param, 1000)
	if err != nil {
		zlog.Errorf(ctx, "batch get staff info failed, err: %v", err)
		return
	}

	// 3. 映射部分枚举字段
	mappings := processMapping(confInfo)

	// 4. 转换数据结构
	rsp.Total = int64(len(allStaffInfo))
	rsp.List = make([]dtonotice.NoticeUserInfo, 0, len(allStaffInfo))
	for _, staff := range allStaffInfo {
		userInfo := convertToNoticeUserInfo(staff, mappings)
		rsp.List = append(rsp.List, userInfo)
	}

	return
}

// 分页处理
func processPage(list []dtonotice.NoticeUserInfo, param dtonotice.ReceiverFilterParam) []dtonotice.NoticeUserInfo {
	if len(list) == 0 {
		return nil
	}

	// 当 Page 或 Size 为 0 时，返回所有数据
	if param.Page == 0 || param.Size == 0 {
		return list
	}

	// 2. 分页处理
	total := int64(len(list))
	start := (param.Page - 1) * param.Size
	if start >= total {
		return nil
	}

	end := start + param.Size
	if end > total {
		end = total
	}

	return list[start:end]
}

// 构建映射表
type Mappings struct {
	Grade      map[interface{}]string
	City       map[interface{}]string
	GradeLevel map[interface{}]string
	Subject    map[interface{}]string
}

// 映射部分枚举字段
func processMapping(conf *userprofile.GeneralConfRsp) *Mappings {
	m := &Mappings{
		Grade:      make(map[interface{}]string, len(conf.User.Grade)),
		City:       make(map[interface{}]string, len(conf.User.City)),
		GradeLevel: make(map[interface{}]string),
		Subject:    make(map[interface{}]string, len(conf.User.SubjectMap)),
	}

	// 构建各种映射
	for _, item := range conf.User.Grade {
		m.Grade[item.Value] = item.Name
	}
	for _, item := range conf.User.City {
		m.City[item.Value] = item.Name
	}
	for _, levels := range conf.User.GradeLevelMap {
		for _, level := range levels {
			m.GradeLevel[level.Value] = level.Name
		}
	}
	for _, item := range conf.User.SubjectMap {
		m.Subject[item.Value] = item.Name
	}

	return m
}

// 转换用户信息
func convertToNoticeUserInfo(staff dataproxy.StaffInfo, m *Mappings) dtonotice.NoticeUserInfo {
	var (
		groupPath       = make([]string, 0, len(staff.Organization))
		groupId         = make([]int64, 0, len(staff.Organization))
		groupName       = make([]string, 0, len(staff.Organization))
		productLineName = make([]string, 0, len(staff.Organization))
	)

	for _, org := range staff.Organization {
		groupId = append(groupId, org.GroupId)
		groupName = append(groupName, org.GroupName)
		productLineName = append(productLineName, org.ProductLineName)

		// 构建组织路径
		paths := make([]string, 0, len(org.GroupPaths)+1)
		for _, path := range org.GroupPaths {
			paths = append(paths, path.GroupName)
		}
		groupPath = append(groupPath, strings.Join(paths, "/"))
	}

	return dtonotice.NoticeUserInfo{
		Uid:             staff.StaffUid,
		Name:            staff.StaffName,
		ProductLineName: utils.JoinSlice(productLineName, ";", nil),
		Region:          utils.JoinSlice(staff.StaffCity, ";", m.City),
		Grade:           utils.JoinSlice(staff.StaffGrade, ";", m.Grade),
		GradeLevel:      utils.JoinSlice(staff.StaffGradeLevel, ";", m.GradeLevel),
		Subject:         utils.JoinSlice(staff.StaffSubject, ";", m.Subject),
		GroupId:         utils.JoinSlice(groupId, ";", nil),
		GroupPath:       utils.JoinSlice(groupPath, ";", nil),
		GroupName:       utils.JoinSlice(groupName, ";", nil),
	}
}

// batchGetStaffInfo 批量获取用户信息
func batchGetStaffInfo(ctx *gin.Context, uids []int64, param dtonotice.ReceiverFilterParam, batchSize int) (result []dataproxy.StaffInfo, err error) {
	result = []dataproxy.StaffInfo{}

	// 计算需要执行的批次数
	batchCount := (len(uids)-1)/batchSize + 1
	maxConcurrent := 10
	if batchCount < maxConcurrent {
		maxConcurrent = batchCount
	}

	var lock sync.Mutex
	executor := utils.NewConcurrentExecutor(ctx, maxConcurrent, func(ctx *gin.Context, batchIndex int) error {
		start := batchIndex * batchSize
		end := start + batchSize
		if end > len(uids) {
			end = len(uids)
		}

		// 构造请求参数，过滤空值
		req := dataproxy.StaffInfoListReq{
			StaffUid: uids[start:end], // StaffUid 必传
		}

		// 只有在非空时才添加筛选条件
		if len(param.ProductLine) > 0 {
			req.ProductLine = param.ProductLine
		}
		if param.GroupId != "" {
			req.GroupId = []int64{cast.ToInt64(param.GroupId)}
		}
		if len(param.Region) > 0 {
			req.StaffCity = param.Region
		}
		if len(param.Grade) > 0 {
			req.StaffGrade = param.Grade
		}
		if len(param.Subject) > 0 {
			req.StaffSubject = param.Subject
		}
		if len(param.GradeLevel) > 0 {
			req.StaffGradeLevel = param.GradeLevel
		}
		if len(param.StaffName) > 0 {
			req.StaffName = []string{param.StaffName}
		}

		batchRsp, err := dataproxy.GetStaffInfoListByFilter(ctx, req)
		if err != nil {
			return err
		}

		lock.Lock()
		result = append(result, batchRsp.List...)
		lock.Unlock()
		return nil
	})

	// 生成批次索引序列
	batchIndexes := utils.GenerateSequence(batchCount)
	err = executor.Execute(batchIndexes)
	if err != nil {
		zlog.Errorf(ctx, "批量获取用户信息失败: %v", err)
		return nil, err
	}

	return
}
