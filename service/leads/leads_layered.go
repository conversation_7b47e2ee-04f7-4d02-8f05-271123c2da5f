package leads

import (
	"assistantdeskgo/api/dataproxy"
	"assistantdeskgo/dto/dtoleads"
	"encoding/json"
	"errors"
	"sort"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// 常量
const (
	dateFormatYYYYMMDD    = "20060102"
	dateFormatMMDD        = "01/02"
	dateFormatChineseMMDD = "01月02日"
	featuresMaxCount      = 20
)

// GetLeadsLayerDetailInfo retrieves layered detail information for a specific course and student.
func GetLeadsLayerDetailInfo(ctx *gin.Context, req dtoleads.GetLeadsLayerDetailInfoReq) (rsp dtoleads.GetLeadsLayerDetailInfoRsp, err error) {
	rsp.Features = make([]dataproxy.Feature, 0, featuresMaxCount)
	rsp.PurchaseIntentions = make([]dtoleads.PurchaseIntention, 0)
	rsp.Milestones = make([]dtoleads.Milestone, 0)

	result, err := dataproxy.GetLeadsAdsBaseModelByCourse(ctx, dataproxy.GetLeadsAdsBaseModelReq{
		CourseId: req.CourseId,
		UserId:   req.StudentUid,
		Fields:   strings.Join([]string{"features", "wx_add_time", "mdc_time", "xzk_time", "inclass_time", "trans_score", "trans_level", "date"}, ","),
	})
	if err != nil {
		return rsp, err
	}
	if len(result.List) == 0 {
		return rsp, errors.New("no models found for the given course and user")
	}

	models := result.List
	sort.Slice(models, func(i, j int) bool {
		return models[i].Date > models[j].Date
	})
	latestModel := models[0]

	rsp.Features, err = buildFeatures(ctx, latestModel)
	if err != nil {
		return rsp, err
	}
	rsp.Milestones = buildMilestones(ctx, latestModel)
	rsp.PurchaseIntentions = buildPurchaseIntentions(ctx, getModelsByAllocTime(ctx, models, req.AllocTime))
	rsp.RefreshTime = formatDate(latestModel.Date, dateFormatChineseMMDD)
	rsp.TransLevel = getTransLevelDesc(latestModel.TransLevel)

	return rsp, nil
}

// buildFeatures constructs a sorted list of features from the model, limited to featuresMaxCount.
func buildFeatures(ctx *gin.Context, model dataproxy.GetLeadsAdsBaseModel) ([]dataproxy.Feature, error) {
	var features []dataproxy.Feature
	if len(model.Features) == 0 {
		return features, nil
	}

	if err := json.Unmarshal([]byte(model.Features), &features); err != nil {
		return nil, err
	}

	if len(features) == 0 {
		return features, nil
	}

	// 获取需要屏蔽的特征配置
	blockedConfig := GetBlockedFeatureConfig(ctx)

	// 过滤掉被屏蔽的特征
	filteredFeatures := make([]dataproxy.Feature, 0, len(features))
	for _, feature := range features {
		isBlocked := false
		for _, blockedName := range blockedConfig.Name {
			if feature.Name == blockedName {
				isBlocked = true
				break
			}
		}
		if !isBlocked {
			filteredFeatures = append(filteredFeatures, feature)
		}
	}
	features = filteredFeatures

	// Sort by weight in descending order
	sort.Slice(features, func(i, j int) bool {
		return features[i].Weight > features[j].Weight
	})

	// Limit to featuresMaxCount
	if len(features) > featuresMaxCount {
		features = features[:featuresMaxCount]
	}

	return features, nil
}

// MilestoneConfig defines the mapping for milestone fields.
type MilestoneConfig struct {
	Timestamp int64
	Desc      string
}

// buildMilestones constructs a list of milestones based on model timestamps.
func buildMilestones(ctx *gin.Context, model dataproxy.GetLeadsAdsBaseModel) []dtoleads.Milestone {
	configs := []MilestoneConfig{
		{model.WxAddTime, "加微时间"},
		{model.MdcTime, "摸底测时间"},
		{model.XzkTime, "1V1出镜时间"},
		{model.InclassTime, "到课时间"},
	}

	milestones := make([]dtoleads.Milestone, 0, len(configs))
	for _, cfg := range configs {
		if cfg.Timestamp > 0 {
			milestones = append(milestones, dtoleads.Milestone{
				Date: formatTimestampToString(cfg.Timestamp, dateFormatMMDD),
				Desc: cfg.Desc,
			})
		}
	}

	return milestones
}

// buildPurchaseIntentions constructs a list of purchase intentions sorted by date.
func buildPurchaseIntentions(ctx *gin.Context, models []dataproxy.GetLeadsAdsBaseModel) []dtoleads.PurchaseIntention {
	if len(models) == 0 {
		return nil
	}

	// Pre-allocate with estimated capacity
	intentions := make([]dtoleads.PurchaseIntention, 0, len(models))

	// Sort models by date in ascending order
	sortedModels := make([]dataproxy.GetLeadsAdsBaseModel, len(models))
	copy(sortedModels, models)
	sort.Slice(sortedModels, func(i, j int) bool {
		return sortedModels[i].Date < sortedModels[j].Date
	})

	for _, model := range sortedModels {
		intentions = append(intentions, dtoleads.PurchaseIntention{
			Date:  formatDate(model.Date, dateFormatMMDD),
			Score: model.TransScore,
		})
	}

	return intentions
}

// getModelsByAllocTime filters models within the date range around allocTime.
func getModelsByAllocTime(ctx *gin.Context, models []dataproxy.GetLeadsAdsBaseModel, allocTime int64) []dataproxy.GetLeadsAdsBaseModel {
	if allocTime <= 0 {
		return nil
	}

	// 这里的时间范围是例子分配时间的当天和后14天
	startDate := time.Unix(allocTime, 0)
	endDate := time.Unix(allocTime, 0).AddDate(0, 0, 14)
	startDateInt := formatTimeToInt(startDate, dateFormatYYYYMMDD)
	endDateInt := formatTimeToInt(endDate, dateFormatYYYYMMDD)

	// Pre-allocate with estimated capacity
	filtered := make([]dataproxy.GetLeadsAdsBaseModel, 0, len(models))
	for _, model := range models {
		if model.Date >= startDateInt && model.Date <= endDateInt {
			filtered = append(filtered, model)
		}
	}

	return filtered
}

// formatTimestampToString formats a Unix timestamp to the specified format.
func formatTimestampToString(timestamp int64, format string) string {
	if timestamp <= 0 {
		return ""
	}
	return time.Unix(timestamp, 0).Format(format)
}

// formatTimeToInt formats a time.Time to an integer date (e.g., 20230101).
func formatTimeToInt(t time.Time, format string) int64 {
	dateStr := t.Format(format)
	return cast.ToInt64(dateStr)
}

func getTransLevelDesc(transLevel int) string {
	switch transLevel {
	case 0:
		return "新"
	case 1:
		return "高"
	case 2:
		return "中"
	case 3:
		return "低"
	default:
		return "新"
	}
}

func formatDate(dateInt int64, format string) string {
	dateStr := cast.ToString(dateInt)
	if len(dateStr) != 8 {
		return ""
	}

	t, err := time.Parse(dateFormatYYYYMMDD, dateStr)
	if err != nil {
		return ""
	}
	return t.Format(format)
}
