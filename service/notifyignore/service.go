package notifyignore

import (
	"assistantdeskgo/api/allocate"
	"assistantdeskgo/api/dal"
	"assistantdeskgo/api/dataproxy"
	"assistantdeskgo/api/dau"
	"assistantdeskgo/api/touchmisgo"
	"assistantdeskgo/api/tower"
	"assistantdeskgo/dto/dtonotifyignore"
	"assistantdeskgo/middleware"
	"assistantdeskgo/utils"
	"time"

	"github.com/gin-gonic/gin"
)

// AddNotifyIgnore 添加消息免打扰配置
func AddNotifyIgnore(ctx *gin.Context, req dtonotifyignore.AddNotifyIgnoreReq) (rsp dtonotifyignore.AddNotifyIgnoreResp, err error) {
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return
	}

	courseLessonInfo, err := dal.GetCourseLessonInfoByCourseId(ctx, req.CourseId, []string{"courseId", "startTime", "stopTime"}, []string{})
	if err != nil {
		return
	}
	startTime := courseLessonInfo.CourseInfo.StartTime
	endTime := courseLessonInfo.CourseInfo.StopTime

	apiReq := touchmisgo.NotifyIgnoreAddReq{
		SourceId:        req.CourseId,
		SourceType:      "course",
		IgnoreStartTime: startTime,
		IgnoreEndTime:   endTime,
		Creator:         int64(userInfo.UserId),
		StudentIds:      req.StudentUids,
	}

	_, err = touchmisgo.NotifyIgnoreAdd(ctx, apiReq)
	if err != nil {
		return
	}

	return
}

// DeleteNotifyIgnore 移除消息免打扰配置
func DeleteNotifyIgnore(ctx *gin.Context, req dtonotifyignore.DeleteNotifyIgnoreReq) (rsp dtonotifyignore.DeleteNotifyIgnoreResp, err error) {
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return
	}

	apiReq := touchmisgo.NotifyIgnoreDeleteReq{
		SourceId:   req.CourseId,
		SourceType: "course",
		Operator:   int64(userInfo.UserId),
		StudentIds: req.StudentUids,
	}

	_, err = touchmisgo.NotifyIgnoreDelete(ctx, apiReq)
	if err != nil {
		return
	}

	return
}

// ListNotifyIgnore 获取消息免打扰配置列表
func ListNotifyIgnore(ctx *gin.Context, req dtonotifyignore.ListNotifyIgnoreReq) (rsp dtonotifyignore.ListNotifyIgnoreResp, err error) {
	// 初始化返回结果
	rsp.List = make([]dtonotifyignore.NotifyIgnoreItem, 0)

	// 根据courseId和assistantUid获取leads信息
	leadsInfoList, err := allocate.GetNormalLeadsByCourseAssistant(ctx, req.CourseId, req.AssistantUid)
	if err != nil || len(leadsInfoList) == 0 {
		return
	}

	// 构建leadsInfoMap和studentUids
	leadsInfoMap := make(map[int64]allocate.GetNormalLeadsByCourseAssistantItem)
	studentUids := make([]int64, 0)
	for _, leadsInfo := range leadsInfoList {
		leadsInfoMap[leadsInfo.StudentUid] = leadsInfo
		studentUids = append(studentUids, leadsInfo.StudentUid)
	}

	// 调用API获取免打扰配置列表
	apiResp, err := touchmisgo.NotifyIgnoreQuery(ctx, touchmisgo.NotifyIgnoreQueryReq{
		SourceId:   req.CourseId,
		SourceType: "course",
		StudentIds: studentUids,
	})
	if err != nil {
		return
	}

	// 如果没有免打扰配置，直接返回空列表
	if len(apiResp.NotifyIgnoreList) == 0 {
		return
	}

	// 批量获取班级名称
	classMap, err := tower.GetGetClassCode(ctx, req.CourseId, req.AssistantUid)
	if err != nil {
		return
	}

	studentIds := make([]int64, 0, len(apiResp.NotifyIgnoreList))
	operatorIds := make([]int64, 0)
	for _, item := range apiResp.NotifyIgnoreList {
		studentIds = append(studentIds, item.StudentId)
		operatorIds = append(operatorIds, item.Operator)
	}

	// 批量获取学生信息
	studentInfoMap, err := dau.GetStudents(ctx, studentIds, []string{"studentName", "phone"})
	if err != nil {
		return
	}

	// 批量获取操作人信息
	operatorResp, err := dataproxy.GetStaffInfoListByFilter(ctx, dataproxy.StaffInfoListReq{
		StaffUid: operatorIds,
	})
	if err != nil {
		return
	}

	operatorInfoMap := make(map[int64]dataproxy.StaffInfo)
	for _, staff := range operatorResp.List {
		operatorInfoMap[staff.StaffUid] = staff
	}

	for _, item := range apiResp.NotifyIgnoreList {
		// 检查学生ID是否在leadsInfoMap中
		leadsInfo, ok := leadsInfoMap[item.StudentId]
		if !ok {
			continue
		}

		// 获取班级ID和名称，检查班级ID是否在classMap中
		classId := leadsInfo.ClassId
		className := "未分班"
		if name, ok := classMap[classId]; ok {
			className = name
		}

		// 获取学生信息
		studentInfo, ok := studentInfoMap[item.StudentId]
		if !ok {
			continue
		}

		// 获取操作人信息
		operatorName := "未知"
		if operator, ok := operatorInfoMap[item.Operator]; ok {
			operatorName = operator.StaffName
		}

		notifyItem := dtonotifyignore.NotifyIgnoreItem{
			StudentUid:  item.StudentId,
			StudentName: studentInfo.StudentName,
			ClassName:   className,
			Phone:       utils.MaskPhone11(studentInfo.Phone),
			StartTime:   formatTime(item.IgnoreStartTime),
			EndTime:     formatTime(item.IgnoreEndTime),
			Operator:    operatorName,
			OperatorUid: item.Operator,
		}

		rsp.List = append(rsp.List, notifyItem)
	}
	rsp.Count = int64(len(rsp.List))

	return
}

// 格式化时间的辅助函数
func formatTime(timestamp int64) string {
	// 将时间戳转换为格式化的时间字符串
	t := time.Unix(timestamp, 0)
	return t.Format("2006-01-02")
}
