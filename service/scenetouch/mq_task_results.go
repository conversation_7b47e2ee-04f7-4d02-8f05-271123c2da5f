package scenetouch

import (
	"assistantdeskgo/api/dal"
	"assistantdeskgo/api/dataproxy"
	"assistantdeskgo/api/examcore"
	"assistantdeskgo/api/moatapi"
	"assistantdeskgo/components"
	"assistantdeskgo/data/scenetpl"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/feedbackv2"
	"assistantdeskgo/dto/scenetouch"
	"assistantdeskgo/models"
	scenetouchmodel "assistantdeskgo/models/scenetouch"
	"assistantdeskgo/service/assistant"
	"fmt"
	"math"
	"math/rand/v2"
	"reflect"
	"slices"
	"strconv"
	"strings"

	"git.zuoyebang.cc/fwyybase/fwyylibs/api/zbtikuapi"
	"git.zuoyebang.cc/fwyybase/fwyylibs/consts"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
)

const (
	LupLessonIdNumMax = 100
)

const (
	VarNameStagePerfectPoint    = "#阶段测优秀知识目标#"
	VarNameStageWeakPoint       = "#阶段测薄弱知识目标#"
	VarNameGoodGreeting         = "#优点表扬#"
	VarNameInclassPointNum      = "#课中知识目标数量#"
	VarNameInclassPerfectPoint  = "#课中优秀知识目标#"
	VarNameInclassGoodPoint     = "#课中良好知识目标#"
	VarNameStageTestQuestionNum = "#阶段测题目总数#"
	VarNameStageTestRightNum    = "#阶段测正确题目数#"
	VarNameStageTestScore       = "#阶段测分数#"
	VarNameStageTestReportUrl   = "#阶段测报告链接#"
)

type GetTaskResultsForPersonalFeedbackPrams struct {
	AssistantUid  int64    `json:"assistantUid"`
	CourseId      int64    `json:"courseId"`
	GroupName     string   `json:"groupName"`
	StudentUids   []int64  `json:"studentUids"`
	NeedVariables []string `json:"needVariables"`
	SceneContext  string   `json:"sceneContext"`
	SceneType     int      `json:"sceneType"`
}

type GetTaskResultsForPersonalFeedbackHandler struct {
	ret              map[int64]*TaskResultMapItem
	courseLessonInfo dal.CourseLessonInfo
	sc               ParsedSceneContextForPersonalFeedback
	needVariables    []string
}

func (h *GetTaskResultsForPersonalFeedbackHandler) prepareCheck(ctx *gin.Context, p GetTaskResultsForPersonalFeedbackPrams, ret map[int64]*TaskResultMapItem) (err error) {
	ret = make(map[int64]*TaskResultMapItem)
	for _, studentUid := range p.StudentUids {
		ret[studentUid] = &TaskResultMapItem{
			StudentUid: studentUid,
			Extra: scenetouch.PrepareTaskExtra{
				FailReasons: make([]string, 0),
			},
			Status:    scenetouchmodel.TaskStatusSuccess,
			Variables: make(map[string]interface{}),
		}
	}
	h.ret = ret

	if _, ok := scenetpl.FeedbackGroupTpl[p.GroupName]; !ok {
		zlog.Warnf(ctx, "GetTaskResultsForPersonalFeedback no config for groupName:%+v", p.GroupName)
		addFailReasonForAll(ctx, ret, p.StudentUids, "未知的分组名："+p.GroupName)
		return components.ErrorParamInvalid
	}

	parsedSc, err := parseSceneContext(ctx, p.SceneType, p.SceneContext)
	if err != nil {
		zlog.Infof(ctx, "parseSceneContextForPersonalFeedback error! sceneContext:%+v, err:%+v", p.SceneContext, err)
		addFailReasonForAll(ctx, ret, p.StudentUids, "系统场景上下文参数格式错误")
		return components.ErrorParamInvalid
	}
	h.sc = parsedSc.PersonalFeedbackContext

	if len(h.sc.LessonIds) > LupLessonIdNumMax {
		zlog.Warnf(ctx, "GetTaskResultsForPersonalFeedback lessonIds > 100")
		addFailReasonForAll(ctx, ret, p.StudentUids, "课中章节选择太多")
		return components.ErrorParamInvalid
	}
	if len(h.sc.LessonIds) == 0 {
		zlog.Warnf(ctx, "GetTaskResultsForPersonalFeedback lessonIds is empty")
		addFailReasonForAll(ctx, ret, p.StudentUids, "课中章节选择为空")
		return components.ErrorParamInvalid
	}

	courseLessonInfo, err := dal.GetCourseLessonInfoByCourseId(ctx, p.CourseId, []string{"courseId", "courseName", "mainGradeId", "cpuId"},
		[]string{"lessonId", "lessonName", "lessonType", "mode", "startTime", "stopTime", "outlineId"})
	if err != nil {
		zlog.Infof(ctx, "dal.GetCourseLessonInfoByCourseId error! err:%+v", err)
		addFailReasonForAll(ctx, ret, p.StudentUids, "课程信息获取失败")
		return components.ErrorParamInvalid
	}
	h.courseLessonInfo = courseLessonInfo
	h.needVariables = p.NeedVariables
	return nil
}

func (h *GetTaskResultsForPersonalFeedbackHandler) needVar(varName string) bool {
	if len(h.needVariables) == 0 {
		return true
	}
	return slices.Contains(h.needVariables, varName)
}

func (h *GetTaskResultsForPersonalFeedbackHandler) stagePoint(ctx *gin.Context, p GetTaskResultsForPersonalFeedbackPrams) {
	// ---------------------------------------------------------------------
	//                 阶段测优秀知识目标、阶段测薄弱知识目标
	// ---------------------------------------------------------------------
	if !h.needVar(VarNameStagePerfectPoint) && !h.needVar(VarNameStageWeakPoint) {
		return
	}
	failStudentUids := make([]int64, 0)

	type StageTestPointInfo struct {
		PointId        int64   `json:"point_id"`
		FirstSubmitCnt int     `json:"first_submit_cnt"`
		FirstRightCnt  int     `json:"first_right_cnt"`
		RightRateFrac  float64 `json:"right_rate_frac"`
	}
	pointIdList := make([]int64, 0)
	stuStageTestPointInfo := make(map[int64]map[int64]*StageTestPointInfo)
	stageTestPointReq := dataproxy.LupLessonsStudentUidsExamTypeReq{
		LessonIds:   fwyyutils.JoinArrayInt64ToString([]int64{h.sc.ExamLessonId}, ","),
		StudentUids: fwyyutils.JoinArrayInt64ToString(p.StudentUids, ","),
		ExamType:    components.ExamTypeStage,
		Fields:      strings.Join([]string{"lesson_id", "student_uid", "point_id", "exam_type", "exam_id", "first_submit_cnt", "first_right_cnt"}, ","),
	}
	stageTestPointRsp, err := dataproxy.GetPointDataByLessonIdsStudentUidsExamType(ctx, stageTestPointReq)
	if err != nil {
		zlog.Infof(ctx, "dataproxy.GetPointDataByLessonIdsStudentUidsExamType error! req:%+v, err:%+v", stageTestPointReq, err)
		if h.needVar(VarNameStagePerfectPoint) {
			addFailReasonForStudents(ctx, h.ret, p.StudentUids, VarNameStagePerfectPoint, "阶段测数据获取失败")
		}
		if h.needVar(VarNameStageWeakPoint) {
			addFailReasonForStudents(ctx, h.ret, p.StudentUids, VarNameStageWeakPoint, "阶段测数据获取失败")
		}
		return
	}
	for _, stageTestPoint := range stageTestPointRsp.List {
		pointIdList = append(pointIdList, stageTestPoint.PointID)
		if _, ok := stuStageTestPointInfo[stageTestPoint.StudentUid]; !ok {
			stuStageTestPointInfo[stageTestPoint.StudentUid] = make(map[int64]*StageTestPointInfo)
		}
		if _, ok := stuStageTestPointInfo[stageTestPoint.StudentUid][stageTestPoint.PointID]; !ok {
			stuStageTestPointInfo[stageTestPoint.StudentUid][stageTestPoint.PointID] = &StageTestPointInfo{
				PointId: stageTestPoint.PointID,
			}
		}
		stuStageTestPointInfo[stageTestPoint.StudentUid][stageTestPoint.PointID].FirstSubmitCnt += stageTestPoint.FirstSubmitCnt
		stuStageTestPointInfo[stageTestPoint.StudentUid][stageTestPoint.PointID].FirstRightCnt += stageTestPoint.FirstRightCnt
	}
	pointIdList = fwyyutils.ArrayUniqInt64(pointIdList)
	pointNameMap := make(map[int64]string)
	if len(pointIdList) > 0 {
		var _err error
		pointNameMap, _err = zbtikuapi.GetPointName(ctx, pointIdList)
		if _err != nil {
			zlog.Infof(ctx, "zbtikuapi.GetPointName error! pointIdList:%+v, err:%+v", pointIdList, _err)
		}
	}
	zlog.Debugf(ctx, "zbtikuapi.GetPointName pointIdList:%+v, pointNameMap:%+v", pointIdList, pointNameMap)

	for stuUid, pointInfoMap := range stuStageTestPointInfo {
		for pointId, pointInfo := range pointInfoMap {
			if pointInfo.FirstSubmitCnt > 0 {
				stuStageTestPointInfo[stuUid][pointId].RightRateFrac = float64(pointInfo.FirstRightCnt) / float64(pointInfo.FirstSubmitCnt)
			} else {
				stuStageTestPointInfo[stuUid][pointId].RightRateFrac = 0
			}
		}
	}
	stuStagePointRightRate := make(map[int64][]StageTestPointInfo)
	for uid, pointInfoMap := range stuStageTestPointInfo {
		for _, pointInfo := range pointInfoMap {
			stuStagePointRightRate[uid] = append(stuStagePointRightRate[uid], *pointInfo)
		}
	}
	for uid, pointRightRateList := range stuStagePointRightRate {
		slices.SortFunc(pointRightRateList, func(a, b StageTestPointInfo) int {
			if math.Abs(a.RightRateFrac-b.RightRateFrac) < 0.000001 {
				return 2*rand.IntN(2) - 1
			} else if a.RightRateFrac > b.RightRateFrac {
				return -1
			} else {
				return 1
			}
		})
		stuStagePointRightRate[uid] = pointRightRateList
	}
	stuStagePerfectPointIdMap := make(map[int64][]int64)
	stuStageWeakPointMap := make(map[int64][]StageTestPointInfo)
	weakPointIdList := make([]int64, 0)
	for uid, pointRightRateList := range stuStagePointRightRate {
		// 优秀知识目标最多取3个
		targetPerfectPointNum := fwyyutils.MinInt(3, int(math.Ceil(float64(len(pointRightRateList))/2)))
		for _, pointInfo := range pointRightRateList {
			// 知识目标正常应该有名称，如果没有名称就过滤掉，避免结果中有空的知识目标名称，下同
			if pointInfo.RightRateFrac > 0 && pointNameMap[pointInfo.PointId] != "" {
				stuStagePerfectPointIdMap[uid] = append(stuStagePerfectPointIdMap[uid], pointInfo.PointId)
				if len(stuStagePerfectPointIdMap[uid]) >= targetPerfectPointNum {
					break
				}
			}
		}
		if len(stuStagePerfectPointIdMap[uid]) == 0 && !slices.Contains(failStudentUids, uid) && h.needVar(VarNameStagePerfectPoint) {
			addFailReason(ctx, h.ret, uid, VarNameStagePerfectPoint, "阶段测无优秀知识目标")
			failStudentUids = append(failStudentUids, uid)
		}
		// 薄弱知识目标最多取2个
		targetWeakPointNum := fwyyutils.MinInt(2, len(pointRightRateList)-targetPerfectPointNum)
		for i := len(pointRightRateList) - 1; i >= 0; i-- {
			pointInfo := pointRightRateList[i]
			if pointNameMap[pointInfo.PointId] == "" {
				continue
			}
			stuStageWeakPointMap[uid] = append(stuStageWeakPointMap[uid], pointInfo)
			weakPointIdList = append(weakPointIdList, pointInfo.PointId)
			if len(stuStageWeakPointMap[uid]) >= targetWeakPointNum {
				break
			}
		}
		if len(stuStageWeakPointMap[uid]) == 0 && !slices.Contains(failStudentUids, uid) && h.needVar(VarNameStageWeakPoint) {
			addFailReason(ctx, h.ret, uid, VarNameStageWeakPoint, "阶段测无薄弱知识目标")
			failStudentUids = append(failStudentUids, uid)
		}
	}

	for _, stuUid := range p.StudentUids {
		perfectPointNames := make([]string, 0)
		for _, pointId := range stuStagePerfectPointIdMap[stuUid] {
			perfectPointNames = append(perfectPointNames, pointNameMap[pointId])
		}
		if len(perfectPointNames) > 0 && h.needVar(VarNameStagePerfectPoint) {
			h.ret[stuUid].Variables[VarNameStagePerfectPoint] = strings.Join(perfectPointNames, "、")
		} else {
			if !slices.Contains(failStudentUids, stuUid) && h.needVar(VarNameStagePerfectPoint) {
				addFailReason(ctx, h.ret, stuUid, VarNameStagePerfectPoint, "阶段测优秀知识目标名称为空")
				failStudentUids = append(failStudentUids, stuUid)
			}
		}
	}
	if !h.needVar(VarNameStageWeakPoint) {
		return
	}

	weakPointIdList = fwyyutils.ArrayUniqInt64(weakPointIdList)
	explainList, err := models.FeedbackPointTargetExplainRef.GetExplainByPointIdList(ctx, h.courseLessonInfo.CpuId, weakPointIdList)
	if err != nil {
		zlog.Infof(ctx, "models.FeedbackPointTargetExplainRef.GetExplainByPointIdList error! pointIdList:%+v, err:%+v", weakPointIdList, err)
	}
	weakPointExplainMap := make(map[int64][]models.FeedbackPointTargetExplain)
	for _, explain := range explainList {
		weakPointExplainMap[explain.PointId] = append(weakPointExplainMap[explain.PointId], explain)
	}
	type WeakPointExplainItem struct {
		PointId    int64   `json:"point_id"`
		PointName  string  `json:"point_name"`
		RightRate  float64 `json:"right_rate"`
		Explain    string  `json:"explain"`
		ErrAnalyze string  `json:"err_analyze"`
	}
	for _, stuUid := range p.StudentUids {
		weekPointItems := make([]*WeakPointExplainItem, 0)
		for _, pointInfo := range stuStageWeakPointMap[stuUid] {
			weakExplainList := weakPointExplainMap[pointInfo.PointId]
			rightFrac := pointInfo.RightRateFrac
			weekPointItems = append(weekPointItems, &WeakPointExplainItem{
				PointId:   pointInfo.PointId,
				PointName: pointNameMap[pointInfo.PointId],
				RightRate: rightFrac,
			})
			for _, explain := range weakExplainList {
				flag := true
				LowerLimit, err1 := strconv.ParseFloat(explain.LowerLimit, 64)
				upperLimit, err2 := strconv.ParseFloat(explain.UpperLimit, 64)
				if err1 != nil || err2 != nil {
					zlog.Infof(ctx, "strconv.ParseFloat error! explain:%+v, err1:%+v, err2:%+v", explain, err1, err2)
					continue
				}
				if explain.LowerIsOpen == models.IsOpenYes {
					flag = flag && (rightFrac > LowerLimit)
				} else {
					flag = flag && (rightFrac >= LowerLimit)
				}
				if !flag {
					continue
				}
				if explain.UpperIsOpen == models.IsOpenYes {
					flag = flag && (rightFrac < upperLimit)
				} else {
					flag = flag && (rightFrac <= upperLimit)
				}
				if !flag {
					continue
				}
				_explain := make([]feedbackv2.ExplainConfItem, 0)
				_err := jsoniter.UnmarshalFromString(explain.ExplainConf, &_explain)
				if _err != nil {
					zlog.Infof(ctx, "jsoniter.UnmarshalFromString error! explain:%+v, err:%+v", explain, _err)
				}
				errAnalyze := ""
				for _, item := range _explain {
					if item.Tag == "错因分析" {
						errAnalyze = item.Text
						break
					}
				}
				weekPointItems[len(weekPointItems)-1].Explain = explain.ExplainConf
				weekPointItems[len(weekPointItems)-1].ErrAnalyze = errAnalyze
				break
			}
		}
		if len(weekPointItems) > 0 {
			weakPointText := make([]string, 0)
			for _, weekPointItem := range weekPointItems {
				if weekPointItem.ErrAnalyze == "" {
					weakPointText = append(weakPointText, weekPointItem.PointName)
				} else {
					weakPointText = append(weakPointText, fmt.Sprintf("%s：%s", weekPointItem.PointName, weekPointItem.ErrAnalyze))
				}
			}
			h.ret[stuUid].Variables[VarNameStageWeakPoint] = strings.Join(weakPointText, "\n")
		} else {
			if !slices.Contains(failStudentUids, stuUid) && h.needVar(VarNameStageWeakPoint) {
				addFailReason(ctx, h.ret, stuUid, VarNameStageWeakPoint, "学员阶段测薄弱知识目标数据缺失")
				failStudentUids = append(failStudentUids, stuUid)
			}
		}
	}
}

func (h *GetTaskResultsForPersonalFeedbackHandler) goodGreet(ctx *gin.Context, p GetTaskResultsForPersonalFeedbackPrams) {
	// ---------------------------------------------------------------------
	//                           优点表扬
	// ---------------------------------------------------------------------
	if !h.needVar(VarNameGoodGreeting) {
		return
	}
	inclassCommonLuReq := dataproxy.LuLessonsStudentsReq{
		LessonIds:   fwyyutils.JoinArrayInt64ToString(h.sc.LessonIds, ","),
		StudentUids: fwyyutils.JoinArrayInt64ToString(p.StudentUids, ","),
		Fields: strings.Join([]string{"lesson_id", "student_uid", "inclass_teacher_room_attend_duration",
			"inclass_teacher_room_total_playback_time_v1", "inclass_participate_cnt", "playback_participate_cnt",
			"inclass_question_cnt", "playback_right_cnt", "inclass_right_cnt", "exam7"}, ","),
	}
	inclassCommonLuRsp, err := dataproxy.GetCommonListByLessonsStudents(ctx, inclassCommonLuReq)
	if err != nil {
		zlog.Infof(ctx, "dataproxy.GetCommonListByLessonsStudents error! req:%+v, err:%+v", inclassCommonLuReq, err)
	}
	inclassLuActionReq := dataproxy.LuCourseLessonIdsStudentUidsAssistantUidReq{
		CourseId:     p.CourseId,
		LessonIds:    fwyyutils.JoinArrayInt64ToString(h.sc.LessonIds, ","),
		StudentUids:  fwyyutils.JoinArrayInt64ToString(p.StudentUids, ","),
		AssistantUid: p.AssistantUid,
		Fields: strings.Join([]string{"course_id", "lesson_id", "student_uid", "assistant_uid", "trade_status",
			"playback_time_in_7d", "homework_first_correct_cnt", "homework_first_right_cnt"}, ","),
	}
	inclassLuActionRsp, err := dataproxy.GetListByCourseIdLessonIdsStudentUidsAssistantUid(ctx, inclassLuActionReq) // idl_assistant_lesson_student_action
	if err != nil {
		zlog.Infof(ctx, "dataproxy.GetListByCourseIdLessonIdsStudentUidsAssistantUid error! req:%+v, err:%+v", inclassLuActionReq, err)
	}
	inclassCommonLuMap := make(map[int64]map[int64]dataproxy.CommonLuItem)            // student_uid, lesson_id
	inclassLuActionMap := make(map[int64]map[int64]dataproxy.LessonStudentActionItem) // student_uid, lesson_id
	for _, lu := range inclassCommonLuRsp.List {
		if _, ok := inclassCommonLuMap[lu.StudentUid]; !ok {
			inclassCommonLuMap[lu.StudentUid] = make(map[int64]dataproxy.CommonLuItem)
		}
		inclassCommonLuMap[lu.StudentUid][lu.LessonID] = lu
	}
	for _, lu := range inclassLuActionRsp.List {
		if _, ok := inclassLuActionMap[lu.StudentUid]; !ok {
			inclassLuActionMap[lu.StudentUid] = make(map[int64]dataproxy.LessonStudentActionItem)
		}
		inclassLuActionMap[lu.StudentUid][lu.LessonID] = lu
	}
	gradeStage := consts.CourseTransMap[h.courseLessonInfo.MainGradeId]
	lessonInfoMap := make(map[int64]dal.LessonInfo)
	for _, lesson := range h.courseLessonInfo.LessonList {
		lessonInfoMap[int64(lesson.LessonId)] = lesson
	}

	// 巩固练习绑定情况
	bindStrs := make([]string, 0)
	lessonBindMap := make(map[string]int64)
	for _, lessonId := range h.sc.LessonIds {
		bindKey := examcore.FormatBindStr(lessonId, components.BuyTypeLesson, components.ExamTypePracticeStrength)
		bindStrs = append(bindStrs, bindKey)
		lessonBindMap[bindKey] = lessonId
		outlineId := int64(lessonInfoMap[lessonId].OutlineId)
		if outlineId > 0 {
			outlineBindKey := examcore.FormatBindStr(outlineId, components.BuyTypeOutline, components.ExamTypePracticeStrength)
			bindStrs = append(bindStrs, outlineBindKey)
			lessonBindMap[outlineBindKey] = lessonId
		}
	}
	getBindReq := examcore.GetBindInfoReq{
		BindStrs: bindStrs,
	}
	bindInfo, err := examcore.GetBindInfo(ctx, getBindReq)
	if err != nil {
		zlog.Warnf(ctx, "examcore.GetBindInfo error! req:%+v, err:%+v", getBindReq, err)
	}
	lessonBindHomework := make(map[int64]bool)
	if bindInfo != nil {
		for bindKey, bindMap := range bindInfo.List {
			lessonId := lessonBindMap[bindKey]
			if lessonId > 0 && len(bindMap) > 0 {
				lessonBindHomework[lessonId] = true
			}
		}
	}

	type GoodGreetData struct {
		AttendNum                   int64   `json:"attendNum"`
		AttendRate                  float64 `json:"attendRate"`
		InteractParticipateNum      int64   `json:"interactParticipateNum"`
		InclassQuestionCnt          int64   `json:"inclassQuestionCnt"`
		InteractParticipateRate     float64 `json:"interactParticipateRate"`
		InteractRightNum            int64   `json:"interactRightNum"`
		InteractRightRate           float64 `json:"interactRightRate"`
		HomeworkSubmitNum           int64   `json:"homeworkSubmitNum"`
		HomeworkAssignNum           int64   `json:"homeworkAssignNum"`
		HomeworkSubmitRate          float64 `json:"homeworkSubmitRate"`
		HomeworkFirstRightNum       int64   `json:"homeworkRightNum"`
		HomeworkFirstParticipateNum int64   `json:"homeworkParticipateNum"`
		HomeworkFirstRightRate      float64 `json:"homeworkRightRate"`
	}
	goodGreetDataMap := make(map[int64]*GoodGreetData)
	for _, stuUid := range p.StudentUids {
		ggd := &GoodGreetData{}
		ggd.HomeworkAssignNum = int64(len(lessonBindHomework))
		if _, ok := inclassCommonLuMap[stuUid]; !ok {
			continue
		}
		for _, lessonId := range h.sc.LessonIds {
			lu := inclassCommonLuMap[stuUid][lessonId]
			ggd.InteractParticipateNum += lu.InclassParticipateCnt + lu.PlaybackParticipateCnt
			ggd.InclassQuestionCnt += lu.InclassQuestionCnt
			ggd.InteractRightNum += lu.InclassRightCnt + lu.PlaybackRightCnt
			exam7Str := lu.Exam7
			exam7 := make(map[string]int64)
			if exam7Str != "" {
				_err := jsoniter.UnmarshalFromString(exam7Str, &exam7)
				if _err != nil {
					zlog.Infof(ctx, "jsoniter.UnmarshalFromString error! item:%+v, exam7Str data:%+v, err:%+v", lu, exam7Str, _err)
					continue
				}
			}
			exam7IsSubmit := exam7["is_submit"]
			if exam7IsSubmit > 0 {
				ggd.HomeworkSubmitNum++
			}
			if lu.InclassTeacherRoomAttendDuration >= defines.SecondsOf30Minutes {
				ggd.AttendNum++
			} else {
				if gradeStage == consts.GradeStageJunior || gradeStage == consts.GradeStageSenior {
					if lu.InclassTeacherRoomTotalPlaybackTimeV1 >= defines.SecondsOf30Minutes {
						ggd.AttendNum++
					} else {
						if _, ok := inclassLuActionMap[stuUid]; !ok {
							continue
						}
						luAction := inclassLuActionMap[stuUid][lessonId]
						if luAction.PlaybackTimeIn7D >= defines.SecondsOf30Minutes && luAction.TradeStatus == consts.TradeStatusPaid {
							ggd.AttendNum++
						}
					}
				}
			}
			if _, ok := inclassLuActionMap[stuUid]; !ok {
				continue
			}
			luAction := inclassLuActionMap[stuUid][lessonId]
			ggd.HomeworkFirstParticipateNum += luAction.HomeworkFirstCorrectCnt
			ggd.HomeworkFirstRightNum += luAction.HomeworkFirstRightCnt
		}
		// 观看到课率
		ggd.AttendRate = float64(ggd.AttendNum) / float64(len(h.sc.LessonIds))
		// 互动参与率
		if ggd.InclassQuestionCnt > 0 {
			ggd.InteractParticipateRate = float64(ggd.InteractParticipateNum) / float64(ggd.InclassQuestionCnt)
		}
		// 互动正确率
		if ggd.InteractParticipateNum > 0 {
			ggd.InteractRightRate = float64(ggd.InteractRightNum) / float64(ggd.InteractParticipateNum)
		}
		// 巩固练习提交
		if ggd.HomeworkAssignNum > 0 {
			ggd.HomeworkSubmitRate = float64(ggd.HomeworkSubmitNum) / float64(ggd.HomeworkAssignNum)
		}
		// 巩固练习首答正确率
		if ggd.HomeworkFirstParticipateNum > 0 {
			ggd.HomeworkFirstRightRate = float64(ggd.HomeworkFirstRightNum) / float64(ggd.HomeworkFirstParticipateNum)
		}
		goodGreetDataMap[stuUid] = ggd
	}
	goodGreetDataMapJson, _ := jsoniter.MarshalToString(goodGreetDataMap)
	zlog.Debugf(ctx, "GetTaskResultsForPersonalFeedback goodGreetDataMap data:%+v", goodGreetDataMapJson)

	goodGreetText := make(map[int64][]string)
	for _, stuUid := range p.StudentUids {
		goodGreetText[stuUid] = make([]string, 0)
		if _, ok := goodGreetDataMap[stuUid]; !ok {
			goodGreetText[stuUid] = []string{"积极参与", "认真听课", "能与老师在课上互动答题"} // 兜底
			continue
		}
		ggd := goodGreetDataMap[stuUid]
		if ggd.AttendRate >= 0.85 {
			goodGreetText[stuUid] = append(goodGreetText[stuUid], "坚持到课")
		}
		if ggd.InteractParticipateRate >= 0.75 {
			goodGreetText[stuUid] = append(goodGreetText[stuUid], "课上积极参与")
		}
		if ggd.InteractRightRate >= 0.7 {
			goodGreetText[stuUid] = append(goodGreetText[stuUid], "答题正确率高")
		}
		if ggd.HomeworkSubmitRate >= 0.5 {
			goodGreetText[stuUid] = append(goodGreetText[stuUid], "坚持完成课后练习")
		}
		if ggd.HomeworkFirstRightRate >= 0.6 {
			goodGreetText[stuUid] = append(goodGreetText[stuUid], "课后练习正确率良好")
		}
		if len(goodGreetText[stuUid]) == 0 {
			goodGreetText[stuUid] = []string{"积极参与", "认真听课", "能与老师在课上互动答题"} // 兜底
		}
	}
	for _, stuUid := range p.StudentUids {
		h.ret[stuUid].Variables[VarNameGoodGreeting] = strings.Join(goodGreetText[stuUid], "、")
	}
}

func (h *GetTaskResultsForPersonalFeedbackHandler) inclassPointNum(ctx *gin.Context, p GetTaskResultsForPersonalFeedbackPrams) {
	// ---------------------------------------------------------------------
	//                          课中知识目标数量
	// ---------------------------------------------------------------------
	if !h.needVar(VarNameInclassPointNum) {
		return
	}
	lpCommonReq := dataproxy.LpCommonPointLessonIdsReq{
		LessonIds: fwyyutils.JoinArrayInt64ToString(h.sc.LessonIds, ","),
		Fields:    strings.Join([]string{"lesson_id", "point_id", "inclass_question_cnt"}, ","),
	}
	lpCommonRspChunk, _err := dataproxy.GetLpPointCommonByLessonIds(ctx, lpCommonReq) // idl_lesson_point_common_action
	if _err != nil {
		zlog.Infof(ctx, "dataproxy.GetLpPointCommonByLessonIds error! req:%+v, err:%+v", lpCommonReq, _err)
		addFailReasonForStudents(ctx, h.ret, p.StudentUids, VarNameInclassPointNum, "章节知识目标数据获取失败")
		return
	}
	commonPointIdSet := make(map[int64]bool)
	for _, lp := range lpCommonRspChunk.List {
		commonPointIdSet[lp.PointID] = true
	}
	// 课中知识目标数量
	for _, stuUid := range p.StudentUids {
		h.ret[stuUid].Variables[VarNameInclassPointNum] = cast.ToString(len(commonPointIdSet))
	}
}

func (h *GetTaskResultsForPersonalFeedbackHandler) inclassPoints(ctx *gin.Context, p GetTaskResultsForPersonalFeedbackPrams) {
	// ---------------------------------------------------------------------
	//                   课中优秀知识目标、课中良好知识目标
	// ---------------------------------------------------------------------
	if !h.needVar(VarNameInclassPerfectPoint) && !h.needVar(VarNameInclassGoodPoint) {
		return
	}
	type PointInfoItem struct {
		PointId        int64   `json:"pointId"`
		RightNum       int64   `json:"rightNum"`
		ParticipateNum int64   `json:"participateNum"`
		QuestionNum    int64   `json:"questionNum"`
		RightRateFrac  float64 `json:"rightRateFrac"`
	}
	failStudentUids := make([]int64, 0)
	stuBatchSize := cast.ToInt(math.Floor(float64(LupLessonIdNumMax) / float64(len(h.sc.LessonIds))))
	if stuBatchSize <= 0 {
		stuBatchSize = 1
	}
	studentUidChunks := fwyyutils.ChunkArrayInt64(p.StudentUids, stuBatchSize)
	stuPointInfo := make(map[int64]map[int64]*PointInfoItem)
	pointIdList := make([]int64, 0)
	for _, stuUidChunk := range studentUidChunks {
		luPointReq := dataproxy.LupLessonsStudentUidsReq{
			LessonIds:   fwyyutils.JoinArrayInt64ToString(h.sc.LessonIds, ","),
			StudentUids: fwyyutils.JoinArrayInt64ToString(stuUidChunk, ","),
			Fields: strings.Join([]string{"lesson_id", "student_uid", "point_id", "inclass_question_cnt",
				"inclass_right_cnt", "inclass_participate_cnt", "playback_right_cnt", "playback_participate_cnt"}, ","),
		}
		luPointRspChunk, _err := dataproxy.GetPointDataByLessonIdsStudentUids(ctx, luPointReq) // idl_lesson_student_point_action
		if _err != nil {
			zlog.Infof(ctx, "dataproxy.GetPointDataByLessonIdsStudentUids error! req:%+v, err:%+v", luPointReq, _err)
			switch scenetpl.FeedbackGroupTpl[p.GroupName][2] {
			case "模板1":
				if h.needVar(VarNameInclassPerfectPoint) {
					addFailReasonForStudents(ctx, h.ret, stuUidChunk, VarNameInclassPerfectPoint, "学员章节知识目标数据获取失败")
					failStudentUids = append(failStudentUids, stuUidChunk...)
				}
			case "模板2":
				if h.needVar(VarNameInclassGoodPoint) {
					addFailReasonForStudents(ctx, h.ret, stuUidChunk, VarNameInclassGoodPoint, "学员章节知识目标数据获取失败")
					failStudentUids = append(failStudentUids, stuUidChunk...)
				}
			}
			continue
		}

		for _, lup := range luPointRspChunk.List {
			pointIdList = append(pointIdList, lup.PointID)
			if _, ok := stuPointInfo[lup.StudentUid]; !ok {
				stuPointInfo[lup.StudentUid] = make(map[int64]*PointInfoItem)
			}
			if _, ok := stuPointInfo[lup.StudentUid][lup.PointID]; !ok {
				stuPointInfo[lup.StudentUid][lup.PointID] = &PointInfoItem{
					PointId:     lup.PointID,
					QuestionNum: lup.InclassQuestionCnt,
				}
			}
			stuPointInfo[lup.StudentUid][lup.PointID].RightNum += lup.InclassRightCnt + lup.PlaybackRightCnt
			stuPointInfo[lup.StudentUid][lup.PointID].ParticipateNum += lup.InclassParticipateCnt + lup.PlaybackParticipateCnt
		}
	}
	pointIdList = fwyyutils.ArrayUniqInt64(pointIdList)
	pointNameMap := make(map[int64]string)
	if len(pointIdList) > 0 {
		var _err error
		pointNameMap, _err = zbtikuapi.GetPointName(ctx, pointIdList)
		if _err != nil {
			zlog.Infof(ctx, "zbtikuapi.GetPointName error! pointIdList:%+v, err:%+v", pointIdList, _err)
		}
	}
	zlog.Debugf(ctx, "zbtikuapi.GetPointName pointIdList:%+v, pointNameMap:%+v", pointIdList, pointNameMap)

	for uid, pointInfoMap := range stuPointInfo {
		for _, pointInfo := range pointInfoMap {
			if pointInfo.ParticipateNum == 0 {
				stuPointInfo[uid][pointInfo.PointId].RightRateFrac = 0
				continue
			}
			stuPointInfo[uid][pointInfo.PointId].RightRateFrac = float64(pointInfo.RightNum) / float64(pointInfo.ParticipateNum)
		}
	}
	type PointRightRate struct {
		PointId       int64   `json:"pointId"`
		RightRateFrac float64 `json:"rightRateFrac"`
	}
	stuPointRightRate := make(map[int64][]PointRightRate)
	for uid, pointInfoMap := range stuPointInfo {
		for _, pointInfo := range pointInfoMap {
			stuPointRightRate[uid] = append(stuPointRightRate[uid], PointRightRate{
				PointId:       pointInfo.PointId,
				RightRateFrac: pointInfo.RightRateFrac,
			})
		}
	}
	for uid, pointRightRateList := range stuPointRightRate {
		slices.SortFunc(pointRightRateList, func(a, b PointRightRate) int {
			if math.Abs(a.RightRateFrac-b.RightRateFrac) < 0.000001 {
				return 2*rand.IntN(2) - 1
			} else if a.RightRateFrac > b.RightRateFrac {
				return -1
			} else {
				return 1
			}
		})
		stuPointRightRate[uid] = pointRightRateList
	}

	stuInclassPointIdMap := make(map[int64][]int64)
	pointIdSet := make(map[int64]bool)
	switch scenetpl.FeedbackGroupTpl[p.GroupName][2] { // 课中学情
	case "模板1": // 课中优秀知识目标
		for _, stuUid := range p.StudentUids {
			if _, ok := stuPointRightRate[stuUid]; !ok {
				if !slices.Contains(failStudentUids, stuUid) && h.needVar(VarNameInclassPerfectPoint) {
					addFailReason(ctx, h.ret, stuUid, VarNameInclassPerfectPoint, "学员知识目标数据缺失")
					failStudentUids = append(failStudentUids, stuUid)
				}
				continue
			}
			if _, ok := stuInclassPointIdMap[stuUid]; !ok {
				stuInclassPointIdMap[stuUid] = make([]int64, 0)
			}
			for _, pointRightRate := range stuPointRightRate[stuUid] {
				if pointRightRate.RightRateFrac >= 0.8 && pointNameMap[pointRightRate.PointId] != "" {
					stuInclassPointIdMap[stuUid] = append(stuInclassPointIdMap[stuUid], pointRightRate.PointId)
					pointIdSet[pointRightRate.PointId] = true
				}
				if len(stuInclassPointIdMap[stuUid]) >= 3 {
					break
				}
			}
		}
	case "模板2": // 课中良好知识目标
		for _, stuUid := range p.StudentUids {
			if _, ok := stuPointRightRate[stuUid]; !ok {
				if !slices.Contains(failStudentUids, stuUid) && h.needVar(VarNameInclassGoodPoint) {
					addFailReason(ctx, h.ret, stuUid, VarNameInclassGoodPoint, "学员知识目标数据缺失")
					failStudentUids = append(failStudentUids, stuUid)
				}
				continue
			}
			if _, ok := stuInclassPointIdMap[stuUid]; !ok {
				stuInclassPointIdMap[stuUid] = make([]int64, 0)
			}
			for _, pointRightRate := range stuPointRightRate[stuUid] {
				if pointRightRate.RightRateFrac >= 0.5 && pointNameMap[pointRightRate.PointId] != "" {
					stuInclassPointIdMap[stuUid] = append(stuInclassPointIdMap[stuUid], pointRightRate.PointId)
					pointIdSet[pointRightRate.PointId] = true
				}
				if len(stuInclassPointIdMap[stuUid]) >= 3 {
					break
				}
			}
		}
	}

	switch scenetpl.FeedbackGroupTpl[p.GroupName][2] {
	case "模板1":
		for _, stuUid := range p.StudentUids {
			if _, ok := stuInclassPointIdMap[stuUid]; !ok {
				if !slices.Contains(failStudentUids, stuUid) && h.needVar(VarNameInclassPerfectPoint) {
					addFailReason(ctx, h.ret, stuUid, VarNameInclassPerfectPoint, "学员知识目标数据缺失")
					failStudentUids = append(failStudentUids, stuUid)
				}
				continue
			}
			pointNames := make([]string, 0)
			for _, pointId := range stuInclassPointIdMap[stuUid] {
				pointNames = append(pointNames, pointNameMap[pointId])
			}
			if len(pointNames) > 0 && h.needVar(VarNameInclassPerfectPoint) {
				h.ret[stuUid].Variables[VarNameInclassPerfectPoint] = strings.Join(pointNames, "、")
			} else {
				if !slices.Contains(failStudentUids, stuUid) && h.needVar(VarNameInclassPerfectPoint) {
					addFailReason(ctx, h.ret, stuUid, VarNameInclassPerfectPoint, "学员知识目标名称缺失")
					failStudentUids = append(failStudentUids, stuUid)
				}
			}
		}
	case "模板2":
		for _, stuUid := range p.StudentUids {
			if _, ok := stuInclassPointIdMap[stuUid]; !ok {
				if !slices.Contains(failStudentUids, stuUid) && h.needVar(VarNameInclassGoodPoint) {
					addFailReason(ctx, h.ret, stuUid, VarNameInclassGoodPoint, "学员知识目标数据缺失")
					failStudentUids = append(failStudentUids, stuUid)
				}
				continue
			}
			pointNames := make([]string, 0)
			for _, pointId := range stuInclassPointIdMap[stuUid] {
				pointNames = append(pointNames, pointNameMap[pointId])
			}
			if len(pointNames) > 0 && h.needVar(VarNameInclassGoodPoint) {
				h.ret[stuUid].Variables[VarNameInclassGoodPoint] = strings.Join(pointNames, "、")
			} else {
				if !slices.Contains(failStudentUids, stuUid) && h.needVar(VarNameInclassGoodPoint) {
					addFailReason(ctx, h.ret, stuUid, VarNameInclassGoodPoint, "学员知识目标名称缺失")
					failStudentUids = append(failStudentUids, stuUid)
				}
			}
		}
	}
}

func (h *GetTaskResultsForPersonalFeedbackHandler) stageTest(ctx *gin.Context, p GetTaskResultsForPersonalFeedbackPrams) {
	// ---------------------------------------------------------------------
	//             阶段测题目总数、阶段测正确题目数、阶段测分数
	// ---------------------------------------------------------------------
	if !h.needVar(VarNameStageTestQuestionNum) && !h.needVar(VarNameStageTestRightNum) && !h.needVar(VarNameStageTestScore) {
		return
	}
	failStudentUids := make([]int64, 0)
	stageLuActionReq := dataproxy.LuCourseLessonIdsStudentUidsAssistantUidReq{
		CourseId:     p.CourseId,
		LessonIds:    fwyyutils.JoinArrayInt64ToString([]int64{h.sc.ExamLessonId}, ","),
		StudentUids:  fwyyutils.JoinArrayInt64ToString(p.StudentUids, ","),
		AssistantUid: p.AssistantUid,
		Fields:       strings.Join([]string{"course_id", "lesson_id", "student_uid", "assistant_uid", "trade_status", "exam_answer"}, ","),
	}
	stageLuActionRsp, err := dataproxy.GetListByCourseIdLessonIdsStudentUidsAssistantUid(ctx, stageLuActionReq) // idl_assistant_lesson_student_action
	if err != nil {
		zlog.Infof(ctx, "dataproxy.GetListByCourseIdLessonIdsStudentUidsAssistantUid error! req:%+v, err:%+v", stageLuActionReq, err)
		if h.needVar(VarNameStageTestQuestionNum) {
			addFailReasonForStudents(ctx, h.ret, p.StudentUids, VarNameStageTestQuestionNum, "阶段测数据获取失败")
		}
		if h.needVar(VarNameStageTestRightNum) {
			addFailReasonForStudents(ctx, h.ret, p.StudentUids, VarNameStageTestRightNum, "阶段测数据获取失败")
		}
		if h.needVar(VarNameStageTestScore) {
			addFailReasonForStudents(ctx, h.ret, p.StudentUids, VarNameStageTestScore, "阶段测数据获取失败")
		}
		return
	}
	stageLuActionMap := make(map[int64]dataproxy.LessonStudentActionItem)
	for _, luAction := range stageLuActionRsp.List {
		if luAction.TradeStatus != consts.TradeStatusPaid {
			if h.needVar(VarNameStageTestQuestionNum) {
				addFailReason(ctx, h.ret, luAction.StudentUid, VarNameStageTestQuestionNum, "学员未购课")
			}
			if h.needVar(VarNameStageTestRightNum) {
				addFailReason(ctx, h.ret, luAction.StudentUid, VarNameStageTestRightNum, "学员未购课")
			}
			if h.needVar(VarNameStageTestScore) {
				addFailReason(ctx, h.ret, luAction.StudentUid, VarNameStageTestScore, "学员未购课")
			}
			if h.needVar(VarNameStageTestQuestionNum) || h.needVar(VarNameStageTestRightNum) || h.needVar(VarNameStageTestScore) {
				failStudentUids = append(failStudentUids, luAction.StudentUid)
			}
			continue
		}
		stageLuActionMap[luAction.StudentUid] = luAction
	}
	for _, stuUid := range p.StudentUids {
		if _, ok := stageLuActionMap[stuUid]; !ok {
			if !slices.Contains(failStudentUids, stuUid) {
				if h.needVar(VarNameStageTestQuestionNum) {
					addFailReason(ctx, h.ret, stuUid, VarNameStageTestQuestionNum, "章节学员数据缺失")
				}
				if h.needVar(VarNameStageTestRightNum) {
					addFailReason(ctx, h.ret, stuUid, VarNameStageTestRightNum, "章节学员数据缺失")
				}
				if h.needVar(VarNameStageTestScore) {
					addFailReason(ctx, h.ret, stuUid, VarNameStageTestScore, "章节学员数据缺失")
				}
				if h.needVar(VarNameStageTestQuestionNum) || h.needVar(VarNameStageTestRightNum) || h.needVar(VarNameStageTestScore) {
					failStudentUids = append(failStudentUids, stuUid)
				}
			}
			continue
		}
		if stageLuActionMap[stuUid].ExamAnswer.Exam9 == nil {
			if !slices.Contains(failStudentUids, stuUid) {
				if h.needVar(VarNameStageTestQuestionNum) {
					addFailReason(ctx, h.ret, stuUid, VarNameStageTestQuestionNum, "学员阶段测数据缺失")
				}
				if h.needVar(VarNameStageTestRightNum) {
					addFailReason(ctx, h.ret, stuUid, VarNameStageTestRightNum, "学员阶段测数据缺失")
				}
				if h.needVar(VarNameStageTestScore) {
					addFailReason(ctx, h.ret, stuUid, VarNameStageTestScore, "学员阶段测数据缺失")
				}
				if h.needVar(VarNameStageTestQuestionNum) || h.needVar(VarNameStageTestRightNum) || h.needVar(VarNameStageTestScore) {
					failStudentUids = append(failStudentUids, stuUid)
				}
			}
			continue
		}

		if stageLuActionMap[stuUid].ExamAnswer.Exam9.AnswerScore != nil {
			score, _e := cast.ToFloat64E(*stageLuActionMap[stuUid].ExamAnswer.Exam9.AnswerScore)
			if _e != nil {
				if !slices.Contains(failStudentUids, stuUid) && h.needVar(VarNameStageTestScore) {
					addFailReason(ctx, h.ret, stuUid, VarNameStageTestScore, "学员阶段测分数据格式错误")
					failStudentUids = append(failStudentUids, stuUid)
				}
			} else {
				if h.needVar(VarNameStageTestScore) {
					roundedScore := math.Round(score/float64(10)*10) / 10
					h.ret[stuUid].Variables[VarNameStageTestScore] = fmt.Sprintf("%g", roundedScore)
				}
			}
		} else {
			if !slices.Contains(failStudentUids, stuUid) && h.needVar(VarNameStageTestScore) {
				addFailReason(ctx, h.ret, stuUid, VarNameStageTestScore, "学员阶段测分数据缺失")
				failStudentUids = append(failStudentUids, stuUid)
			}
		}
		if stageLuActionMap[stuUid].ExamAnswer.Exam9.TotalNum != nil {
			totalNum, _e := cast.ToInt64E(*stageLuActionMap[stuUid].ExamAnswer.Exam9.TotalNum)
			if _e != nil {
				if !slices.Contains(failStudentUids, stuUid) && h.needVar(VarNameStageTestQuestionNum) {
					addFailReason(ctx, h.ret, stuUid, VarNameStageTestQuestionNum, "学员阶段测题数数据格式错误")
					failStudentUids = append(failStudentUids, stuUid)
				}
			} else {
				if h.needVar(VarNameStageTestQuestionNum) {
					h.ret[stuUid].Variables[VarNameStageTestQuestionNum] = cast.ToString(totalNum)
				}
			}
		} else {
			if !slices.Contains(failStudentUids, stuUid) && h.needVar(VarNameStageTestQuestionNum) {
				addFailReason(ctx, h.ret, stuUid, VarNameStageTestQuestionNum, "学员阶段测题数数据缺失")
				failStudentUids = append(failStudentUids, stuUid)
			}
		}
		if stageLuActionMap[stuUid].ExamAnswer.Exam9.RightNum != nil {
			rightNum, _e := cast.ToInt64E(*stageLuActionMap[stuUid].ExamAnswer.Exam9.RightNum)
			if _e != nil {
				if !slices.Contains(failStudentUids, stuUid) && h.needVar(VarNameStageTestRightNum) {
					addFailReason(ctx, h.ret, stuUid, VarNameStageTestRightNum, "学员阶段测正确题数数据格式错误")
					failStudentUids = append(failStudentUids, stuUid)
				}
			} else {
				if h.needVar(VarNameStageTestRightNum) {
					h.ret[stuUid].Variables[VarNameStageTestRightNum] = cast.ToString(rightNum)
				}
			}
		} else {
			if !slices.Contains(failStudentUids, stuUid) && h.needVar(VarNameStageTestRightNum) {
				addFailReason(ctx, h.ret, stuUid, VarNameStageTestRightNum, "学员阶段测正确题数数据缺失")
				failStudentUids = append(failStudentUids, stuUid)
			}
		}
	}
}

func (h *GetTaskResultsForPersonalFeedbackHandler) stageReport(ctx *gin.Context, p GetTaskResultsForPersonalFeedbackPrams) {
	// ---------------------------------------------------------------------
	//                            阶段测报告链接
	// ---------------------------------------------------------------------
	if !h.needVar(VarNameStageTestReportUrl) {
		return
	}

	//// 上线前移除
	//for _, stuUid := range p.StudentUids {
	//	h.ret[stuUid].Variables[VarNameStageTestReportUrl] = "https://zyburl.com/su/aBcDeF"
	//}
	//return

	failStudentUids := make([]int64, 0)
	bridgeRet, err := assistant.GetStageTestReportFromBridge(ctx, p.CourseId, h.sc.ExamLessonId, p.StudentUids)
	if err != nil {
		zlog.Infof(ctx, "assistant.GetStageTestReportFromBridge error! err:%+v", err)
		addFailReasonForStudents(ctx, h.ret, p.StudentUids, VarNameStageTestReportUrl, "阶段测报告链接获取失败")
		return
	}
	getRetMap := func(res interface{}) map[string]interface{} {
		resVal := reflect.ValueOf(res)
		switch resVal.Kind() {
		case reflect.Map:
			_ret := make(map[string]interface{})
			for _, key := range resVal.MapKeys() {
				_ret[key.String()] = resVal.MapIndex(key).Interface()
			}
			return _ret
		}
		return make(map[string]interface{})
	}
	retMap := getRetMap(bridgeRet.Res)
	reportUrlMap := make(map[int64]string)
	for studentUidStr, reportUrlsInf := range retMap {
		curStudentUid, _err3 := strconv.ParseInt(studentUidStr, 10, 64)
		if _err3 != nil {
			zlog.Infof(ctx, "strconv.ParseInt error! studentUidStr:%+v, err:%+v", studentUidStr, _err3)
			continue
		}
		reportUrlsWrap := reportUrlsInf.(map[string]interface{})
		reportUrls := make(map[string]interface{})
		for _, mapInf := range reportUrlsWrap {
			reportUrls = mapInf.(map[string]interface{})
			break
		}
		reportUrl := ""
		for fieldKey, reportUrlStrInf := range reportUrls {
			if fieldKey == "lookurl" {
				reportUrl = reportUrlStrInf.(string)
				break
			}
		}
		reportUrlMap[curStudentUid] = reportUrl
	}
	for _, stuUid := range p.StudentUids {
		if reportUrlMap[stuUid] == "" {
			if in, _ := fwyyutils.InArrayInt64(stuUid, failStudentUids); !in {
				addFailReason(ctx, h.ret, stuUid, VarNameStageTestReportUrl, "学员阶段测报告链接获取失败")
				failStudentUids = append(failStudentUids, stuUid)
			}
			continue
		}
		reportUrl := reportUrlMap[stuUid]
		shortUrl, _err := moatapi.GetShortUrl(ctx, reportUrl)
		if _err != nil {
			zlog.Infof(ctx, "moatapi.GetShortUrl error! reportUrl:%+v, err:%+v", reportUrl, _err)
			h.ret[stuUid].Variables[VarNameStageTestReportUrl] = reportUrl
		} else {
			h.ret[stuUid].Variables[VarNameStageTestReportUrl] = shortUrl
		}

	}
}

// GetTaskResultsForPersonalFeedback 个性化学情反馈 https://docs.zuoyebang.cc/doc?fileId=1877618053743484929
func GetTaskResultsForPersonalFeedback(ctx *gin.Context, p GetTaskResultsForPersonalFeedbackPrams) (ret map[int64]*TaskResultMapItem, err error) {
	defer func() {
		if r := recover(); r != nil {
			zlog.Errorf(ctx, "GetTaskResultsForPersonalFeedback panic! err:%+v", r)
		}
	}()

	h := &GetTaskResultsForPersonalFeedbackHandler{}
	err = h.prepareCheck(ctx, p, ret)
	if err != nil {
		zlog.Infof(ctx, "prepareCheck error! p:%+v, err:%+v", p, err)
		return h.ret, err
	}

	// 阶段测优秀知识目标、阶段测薄弱知识目标
	h.stagePoint(ctx, p)

	// 优点表扬
	h.goodGreet(ctx, p)

	// 课中知识目标数量
	h.inclassPointNum(ctx, p)

	// 课中优秀知识目标、课中良好知识目标
	h.inclassPoints(ctx, p)

	// 阶段测题目总数、阶段测正确题目数、阶段测分数
	h.stageTest(ctx, p)

	// 阶段测报告链接
	h.stageReport(ctx, p)

	return h.ret, nil
}
