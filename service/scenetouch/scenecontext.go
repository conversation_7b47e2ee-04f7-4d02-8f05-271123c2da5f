package scenetouch

import (
	"assistantdeskgo/api/dal"
	"assistantdeskgo/api/examcore"
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/scenetouch"
	"fmt"
	dalconst "git.zuoyebang.cc/fwyybase/fwyylibs/consts/dal"
	"git.zuoyebang.cc/fwyybase/fwyylibs/consts/touchmis"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"sort"
	"strings"
	"time"
)

func ContextOptions(ctx *gin.Context, req scenetouch.ContextOptionsReq) (rsp scenetouch.ContextOptionsRsp, err error) {
	if req.Key == "" {
		return rsp, components.ErrorParamInvalid
	}
	switch req.Key {
	case defines.SceneContextOptionExam9: // 阶段测
		rsp, err = BuildOptionsForExam9(ctx, req)
	case defines.SceneContextOptionLesson:
		rsp, err = BuildOptionsForLesson(ctx, req)
	default:
		return rsp, components.ErrorParamInvalid
	}

	if err != nil {
		zlog.Infof(ctx, "buildOptions error! req:%+v, err:%+v", req, err)
		return
	}

	switch req.SceneType {
	case touchmis.SendTypePersonalLearnFeedback: // 个性化学情反馈
		switch req.Key {
		case defines.SceneContextOptionExam9:
			for i, item := range rsp.List {
				// 阶段册的解锁时间是 章节下课后
				lessonTime := cast.ToInt64(item.Extra["lessonStopTime"])
				rsp.List[i].CanSelect = time.Now().Unix() > lessonTime
			}
		case defines.SceneContextOptionLesson:
			for i, item := range rsp.List {
				lessonType := cast.ToInt(item.Extra["lessonType"])
				lessonMode := cast.ToInt(item.Extra["mode"])
				lessonTime := cast.ToInt64(item.Extra["stopTime"])
				official := lessonType == dalconst.LessonTypeCore && (lessonMode == 0 || lessonMode == dalconst.OutlineModeFormal)
				finish := time.Now().Unix() > lessonTime
				rsp.List[i].CanSelect = official && finish
			}
		}
	}

	return
}

func BuildOptionsForExam9(ctx *gin.Context, req scenetouch.ContextOptionsReq) (rsp scenetouch.ContextOptionsRsp, err error) {
	if req.CourseId <= 0 {
		return rsp, components.ErrorParamInvalid
	}

	var courseLessonInfo dal.CourseLessonInfo
	courseLessonInfo, err = dal.GetCourseLessonInfoByCourseId(ctx, req.CourseId, []string{"courseId", "courseName", "mainGradeId", "cpuId"},
		[]string{"lessonId", "lessonName", "startTime", "stopTime", "outlineId"})
	if err != nil {
		zlog.Warnf(ctx, "dal.GetCourseLessonInfoByCourseId error! courseId:%+v, err:%+v", req.CourseId, err)
		return
	}

	lessonList := make([]dal.LessonInfo, 0)
	for _, lesson := range courseLessonInfo.LessonList {
		lessonList = append(lessonList, lesson)
	}
	sort.Slice(lessonList, func(i, j int) bool {
		return lessonList[i].StartTime < lessonList[j].StartTime
	})

	lessonExamIdsMap, err := examcore.GetLessonBindExamIds(ctx, lessonList, components.ExamTypeStage)
	if err != nil {
		zlog.Warnf(ctx, "examcore.GetLessonBindExamIds error! lessonList:%+v, err:%+v", lessonList, err)
		return
	}

	i := 1
	for _, lesson := range lessonList {
		if len(lessonExamIdsMap[lesson.LessonId]) == 0 {
			continue
		}
		examIds := make([]int64, 0)
		for _, examIdStr := range lessonExamIdsMap[lesson.LessonId] {
			examId, _err := fwyyutils.ToInt64(examIdStr)
			if _err != nil {
				zlog.Warnf(ctx, "fwyyutils.ToInt64 error! examIdStr:%+v, err:%+v", examIdStr, _err)
				return rsp, _err
			}
			examIds = append(examIds, examId)
		}
		sort.Slice(examIds, func(i, j int) bool {
			return examIds[i] < examIds[j]
		})
		for _, examId := range examIds {
			rsp.List = append(rsp.List, scenetouch.ContextOptionItem{
				Label: fmt.Sprintf("阶段测%d", i),
				// 多个章节可以绑定同一阶段测
				Value:         fmt.Sprintf("%d_%d", examId, lesson.LessonId),
				CanSelect:     true,
				DefaultSelect: false,
				Extra: map[string]interface{}{
					"lessonStartTime": lesson.StartTime,
					"lessonStopTime":  lesson.StopTime,
				},
			})
			i++
		}
	}

	return
}

func BuildOptionsForLesson(ctx *gin.Context, req scenetouch.ContextOptionsReq) (rsp scenetouch.ContextOptionsRsp, err error) {
	if req.CourseId <= 0 {
		return rsp, components.ErrorParamInvalid
	}

	courseLessonInfo, err := dal.GetCourseLessonInfoByCourseId(ctx, req.CourseId, []string{"courseId", "courseName", "mainGradeId", "cpuId"},
		[]string{"lessonId", "lessonName", "lessonType", "mode", "startTime", "stopTime"})
	if err != nil {
		zlog.Infof(ctx, "dal.GetCourseLessonInfoByCourseId error! err:%+v", err)
		return rsp, components.DefaultError("获取章节信息失败")
	}
	lessonList := make([]dal.LessonInfo, 0)
	for _, lesson := range courseLessonInfo.LessonList {
		lessonList = append(lessonList, lesson)
	}
	sort.Slice(lessonList, func(i, j int) bool {
		return lessonList[i].StartTime < lessonList[j].StartTime
	})

	for _, lesson := range lessonList {
		rsp.List = append(rsp.List, scenetouch.ContextOptionItem{
			Label:         fmt.Sprintf("%s", lesson.LessonName),
			Value:         int64(lesson.LessonId),
			CanSelect:     true,
			DefaultSelect: false,
			Extra: map[string]interface{}{
				"lessonType": lesson.LessonType,
				"mode":       lesson.Mode,
				"startTime":  lesson.StartTime,
				"stopTime":   lesson.StopTime,
			},
		})
	}

	return
}

type ParsedSceneContextForPersonalFeedback struct {
	ExamId       int64   `json:"examId"`
	ExamLessonId int64   `json:"examLessonId"`
	LessonIds    []int64 `json:"lessonIds"`
}
type ParsedSceneContext struct {
	PersonalFeedbackContext ParsedSceneContextForPersonalFeedback
}

func convertValue[T any](ctx *gin.Context, value interface{}) (val T, err error) {
	bytes, err := jsoniter.Marshal(value)
	if err != nil {
		zlog.Infof(ctx, "convertValue Marshal error! value:%+v, err:%+v", value, err)
		return val, err
	}
	err = jsoniter.Unmarshal(bytes, &val)
	if err != nil {
		zlog.Infof(ctx, "convertValue Unmarshal error! err:%+v", err)
		return val, err
	}
	return val, nil
}

func parseSceneContext(ctx *gin.Context, sceneType int, sceneContext string) (sc ParsedSceneContext, err error) {
	if sceneContext == "" {
		return sc, components.ErrorParamInvalid
	}
	type SceneContextItem struct {
		Key   string      `json:"key"`
		Value interface{} `json:"value"`
	}
	switch sceneType {
	case touchmis.SendTypePersonalLearnFeedback:
		scArr := make([]SceneContextItem, 0)
		err := jsoniter.UnmarshalFromString(sceneContext, &scArr)
		if err != nil {
			zlog.Infof(ctx, "parseSceneContext unmarshal sceneContext error! sceneContext:%+v, err:%+v", sceneContext, err)
			return sc, err
		}
		for _, item := range scArr {
			switch item.Key {
			case defines.SceneContextOptionExam9:
				val, _err := convertValue[scenetouch.ContextOptionItem](ctx, item.Value)
				if _err != nil {
					return sc, components.ErrorParamInvalid
				}
				// 格式：examId_lessonId
				parts := strings.Split(cast.ToString(val.Value), "_")
				if len(parts) != 2 {
					zlog.Infof(ctx, "parseSceneContext error! exam9 val.Value parts len error! sceneContext:%+v", sceneContext)
					return sc, components.ErrorParamInvalid
				}
				sc.PersonalFeedbackContext.ExamId, _err = cast.ToInt64E(parts[0])
				if _err != nil {
					return sc, components.ErrorParamInvalid
				}
				sc.PersonalFeedbackContext.ExamLessonId, _err = cast.ToInt64E(parts[1])
				if _err != nil {
					return sc, components.ErrorParamInvalid
				}
			case defines.SceneContextOptionLesson:
				val, _err := convertValue[[]scenetouch.ContextOptionItem](ctx, item.Value)
				if _err != nil {
					return sc, components.ErrorParamInvalid
				}
				var lessonId int64
				for _, iv := range val {
					lessonId, _err = cast.ToInt64E(iv.Value)
					if _err != nil {
						return sc, components.ErrorParamInvalid
					}
					sc.PersonalFeedbackContext.LessonIds = append(sc.PersonalFeedbackContext.LessonIds, lessonId)
				}
			}
		}
	}
	return sc, nil
}
