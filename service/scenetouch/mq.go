package scenetouch

import (
	"assistantdeskgo/components"
	dtoscenetouch "assistantdeskgo/dto/scenetouch"
	"assistantdeskgo/helpers"
	scenetouchmodel "assistantdeskgo/models/scenetouch"
	"assistantdeskgo/utils"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"

	"git.zuoyebang.cc/fwyybase/fwyylibs/consts/touchmis"

	"git.zuoyebang.cc/pkg/golib/v2/gomcpack/mcpack"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"gorm.io/gorm"
)

const (
	RedisLockPrepareJob = "deskgo_prepare_job:%v"
)

type TaskResultMapItem struct {
	StudentUid int64                          `json:"studentUid"`
	Status     int                            `json:"status"`
	Variables  map[string]interface{}         `json:"variables"`
	Extra      dtoscenetouch.PrepareTaskExtra `json:"extra"`
}

type GetTaskResultsParams struct {
	SceneType     int      `json:"sceneType"`
	AssistantUid  int64    `json:"assistantUid"`
	CourseId      int64    `json:"courseId"`
	LessonId      int64    `json:"lessonId"`
	GroupName     string   `json:"groupName"`
	StudentUids   []int64  `json:"studentUids"`
	NeedVariables []string `json:"needVariables"`
	SceneContext  string   `json:"sceneContext"`
}

func getTaskResults(ctx *gin.Context, param GetTaskResultsParams) (ret map[int64]*TaskResultMapItem, err error) {
	if len(param.StudentUids) == 0 {
		zlog.Infof(ctx, "getTaskResults studentUids is empty")
		return ret, nil
	}

	switch param.SceneType {
	case touchmis.SendTypePersonalLearnFeedback:
		param := GetTaskResultsForPersonalFeedbackPrams{
			AssistantUid:  param.AssistantUid,
			CourseId:      param.CourseId,
			GroupName:     param.GroupName,
			StudentUids:   param.StudentUids,
			NeedVariables: param.NeedVariables,
			SceneContext:  param.SceneContext,
			SceneType:     param.SceneType,
		}
		ret, err = GetTaskResultsForPersonalFeedback(ctx, param)
		if err != nil {
			zlog.Errorf(ctx, "GetTaskResultsForPersonalFeedback error! param:%+v, err:%+v", param, err)
			return ret, err
		}
	default:
		return nil, components.ErrorParamInvalid
	}

	return ret, nil
}

// addFailReasonForAll 系统性问题，会导致学员多变量异常
func addFailReasonForAll(ctx *gin.Context, ret map[int64]*TaskResultMapItem, studentUids []int64, _failReason string) {
	for _, studentUid := range studentUids {
		ret[studentUid].Status = scenetouchmodel.TaskStatusFail
		ret[studentUid].Extra.FailReasons = append(ret[studentUid].Extra.FailReasons, _failReason)
		zlog.Infof(ctx, "addFailReason studentUid:%+v, failReason:%+v", studentUid, _failReason)
	}
}

func addFailReasonForStudents(ctx *gin.Context, ret map[int64]*TaskResultMapItem, _studentUids []int64, _varName string, _reason string) {
	for _, studentUid := range _studentUids {
		ret[studentUid].Status = scenetouchmodel.TaskStatusFail
		failReason := fmt.Sprintf("由于%s，%s替换失败", _reason, _varName)
		ret[studentUid].Extra.FailReasons = append(ret[studentUid].Extra.FailReasons, failReason)
		zlog.Infof(ctx, "addFailReason studentUid:%+v, failReason:%+v", studentUid, failReason)
	}
}

func addFailReason(ctx *gin.Context, ret map[int64]*TaskResultMapItem, _studentUid int64, _varName string, _reason string) {
	ret[_studentUid].Status = scenetouchmodel.TaskStatusFail
	failReason := fmt.Sprintf("由于%s，%s替换失败", _reason, _varName)
	ret[_studentUid].Extra.FailReasons = append(ret[_studentUid].Extra.FailReasons, failReason)
	zlog.Infof(ctx, "addFailReason studentUid:%+v, failReason:%+v", _studentUid, failReason)
}

func HandleSceneTouchPrepare(ctx *gin.Context, p mcpack.V2Map) (err error) {
	defer func() {
		if err != nil {
			zlog.Errorf(ctx, "HandleSceneTouchPrepare error, err:%v", err)
		}

		if errPanic := recover(); errPanic != nil {
			zlog.Errorf(ctx, "HandleSceneTouchPrepare panic, err:%v", errPanic)
		}
	}()

	msg, err := convertMqMsg(ctx, p)
	if err != nil {
		zlog.Warnf(ctx, "HandleSceneTouchPrepare convertMqMsg error, p:%+v, err:%v", p, err)
		return nil
	}

	taskList, err := scenetouchmodel.TblSceneTouchPrepareTaskRef.GetTaskListByIdList(ctx, msg.TaskIdList)
	if err != nil {
		zlog.Warnf(ctx, "JobResult GetTaskListByIdList error! mqMsg:%+v, err:%+v", msg, err)
		// 可能是主从延迟等原因导致获取失败，需要重新消费
		return err
	}
	if len(taskList) == 0 {
		zlog.Warnf(ctx, "JobResult taskList is empty! mqMsg:%+v", msg)
		return nil
	}

	jobLockVal := uuid.New().String()
	lock, err := getJobLock(ctx, msg.JobId, jobLockVal)
	if err != nil {
		zlog.Infof(ctx, "getJobLock error! mqMsg:%+v,err:%+v", msg, err)
		return err
	}
	if !lock {
		zlog.Infof(ctx, "getJobLock fail! mqMsg:%+v", msg)
		return errors.New("getJobLock fail")
	}

	job, err := scenetouchmodel.TblSceneTouchPrepareJobRef.GetJobById(ctx, msg.JobId, true)
	if err != nil {
		releaseJobLock(ctx, msg.JobId, jobLockVal)
		zlog.Warnf(ctx, "JobResult GetJobById error! mqMsg:%+v, err:%+v", msg, err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 由于是走主库读取，没有获取到job，可能是由于job已被删除或传入了不存在的jobId，跳过处理
			return nil
		}
		return err
	}
	if job == nil {
		releaseJobLock(ctx, msg.JobId, jobLockVal)
		zlog.Warnf(ctx, "JobResult job is nil! mqMsg:%+v", msg)
		return nil
	}

	if job.Status == scenetouchmodel.JobStatusDone || job.Status == scenetouchmodel.JobStatusCancel {
		releaseJobLock(ctx, msg.JobId, jobLockVal)
		zlog.Infof(ctx, "JobResult job is done or cancel! mqMsg:%+v", msg)
		return nil
	}
	sceneType := job.SceneType
	todoTaskList := make([]*scenetouchmodel.TblSceneTouchPrepareTask, 0)
	for _, task := range taskList {
		if task == nil {
			zlog.Warnf(ctx, "JobResult task is nil! mqMsg:%+v", msg)
			continue
		}
		if task.GroupName != msg.GroupName {
			zlog.Infof(ctx, "JobResult taskGroupName not match! mqMsg:%+v, taskId:%d, taskGroupName:%s, msgGroupName:%s", msg, task.Id, task.GroupName, msg.GroupName)
			continue
		}
		if task.Status == scenetouchmodel.TaskStatusInit {
			todoTaskList = append(todoTaskList, task)
		} else if task.Status == scenetouchmodel.TaskStatusRunning {
			zlog.Infof(ctx, "JobResult task is running! mqMsg:%+v, taskId:%+v", msg, task.Id)
			if time.Now().Unix()-task.UpdateTime > taskRerunTimeout(sceneType) {
				zlog.Infof(ctx, "JobResult task is timeout, rerun it! mqMsg:%+v, taskId:%+v", msg, task.Id)
				todoTaskList = append(todoTaskList, task)
			}
		}
	}
	if len(todoTaskList) == 0 {
		releaseJobLock(ctx, msg.JobId, jobLockVal)
		zlog.Infof(ctx, "JobResult todo task list is empty! mqMsg:%+v", msg)
		return nil
	}
	studentUids := make([]int64, 0)
	for _, task := range todoTaskList {
		studentUids = append(studentUids, task.StudentUid)
	}

	err = startJobAndTasks(ctx, job, todoTaskList)
	releaseJobLock(ctx, msg.JobId, jobLockVal)
	if err != nil {
		zlog.Errorf(ctx, "JobResult startJobAndTasks error! sceneType:%+v, job:%+v, mqMsg:%+v, studentUids:%+v, err:%+v", sceneType, job, msg, studentUids, err)
		return err
	}

	// 变量加上#
	needVariables := make([]string, 0)
	for _, needVar := range msg.NeedVariables {
		if needVar == "" {
			continue
		}
		if !strings.HasPrefix(needVar, "#") && !strings.HasSuffix(needVar, "#") {
			needVariables = append(needVariables, "#"+needVar+"#")
		}
	}
	ret, err := getTaskResults(ctx, GetTaskResultsParams{
		SceneType:     sceneType,
		AssistantUid:  job.AssistantUid,
		CourseId:      job.CourseId,
		LessonId:      job.LessonId,
		GroupName:     msg.GroupName,
		StudentUids:   studentUids,
		NeedVariables: needVariables,
		SceneContext:  job.SceneContext,
	})
	if err != nil {
		zlog.Errorf(ctx, "JobResult getTaskResults error! sceneType:%+v, job:%+v, mqMsg:%+v, studentUids:%+v, err:%+v", sceneType, job, msg, studentUids, err)
		return err
	}
	if ret == nil {
		zlog.Errorf(ctx, "JobResult getTaskResults error! getTaskResults ret is nil! sceneType:%+v, job:%+v, mqMsg:%+v, studentUids:%+v", sceneType, job, msg, studentUids)
		return errors.New("getTaskResults ret is nil")
	}
	_retJsonStr, _ := jsoniter.MarshalToString(ret)
	zlog.Debugf(ctx, "JobResult getTaskResults detail sceneType:%+v, job:%+v, mqMsg:%+v, studentUids:%+v, ret:%+v", sceneType, job, msg, studentUids, _retJsonStr)

	lock, err = getJobLock(ctx, job.Id, jobLockVal)
	if err != nil {
		zlog.Infof(ctx, "getJobLock error! sceneType:%+v, job:%+v, mqMsg:%+v, studentUids:%+v, err:%+v", sceneType, job, msg, studentUids, err)
		return err
	}
	if !lock {
		zlog.Infof(ctx, "getJobLock fail! sceneType:%+v, job:%+v, mqMsg:%+v, studentUids:%+v", sceneType, job, msg, studentUids)
		return errors.New("getJobLock fail")
	}

	job, err = scenetouchmodel.TblSceneTouchPrepareJobRef.GetJobById(ctx, msg.JobId, true)
	if err != nil {
		releaseJobLock(ctx, msg.JobId, jobLockVal)
		zlog.Warnf(ctx, "JobResult GetJobById error! mqMsg:%+v, err:%+v", msg, err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		}
		return err
	}
	if job == nil {
		releaseJobLock(ctx, msg.JobId, jobLockVal)
		zlog.Warnf(ctx, "JobResult job is nil! mqMsg:%+v", msg)
		return nil
	}

	err = helpers.MysqlClientFuDao.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, task := range todoTaskList {
			result, ok := ret[task.StudentUid]
			if !ok {
				if err := setTaskFail(ctx, job, task, tx); err != nil {
					return err
				}
				continue
			}
			if result.Variables != nil {
				variablesStr, err := jsoniter.MarshalToString(result.Variables)
				if err != nil {
					return err
				}
				task.Variables = variablesStr
			}
			extraStr, err := jsoniter.MarshalToString(result.Extra)
			if err != nil {
				return err
			}
			task.Extra = extraStr
			if result.Status == scenetouchmodel.TaskStatusSuccess {
				if err := setTaskSuccess(ctx, job, task, tx); err != nil {
					return err
				}
			} else {
				if err := setTaskFail(ctx, job, task, tx); err != nil {
					return err
				}
			}
		}
		return nil
	})
	releaseJobLock(ctx, msg.JobId, jobLockVal)
	if err != nil {
		zlog.Errorf(ctx, "JobResult update task results error! sceneType:%+v, job:%+v, mqMsg:%+v, studentUids:%+v, err:%+v", sceneType, job, msg, studentUids, err)
		return err
	}

	return nil
}

func taskRerunTimeout(sceneType int) (timeout int64) {
	timeout = 2
	return
}

func convertMqMsg(ctx *gin.Context, p mcpack.V2Map) (msg dtoscenetouch.PrepareTaskMQ, err error) {
	pStr, err := jsoniter.MarshalToString(p)
	if err != nil {
		zlog.Errorf(ctx, "HandleSceneTouchPrepare MarshalToString error! p:%+v, err:%+v", p, err)
		return msg, err
	}
	zlog.Debugf(ctx, "HandleSceneTouchPrepare_Param: %+v", pStr)
	if err = jsoniter.UnmarshalFromString(pStr, &msg); err != nil {
		zlog.Errorf(ctx, "HandleSceneTouchPrepare UnmarshalFromString error! pStr:%+v, err:%+v", pStr, err)
		return msg, err
	}
	if msg.JobId <= 0 || len(msg.TaskIdList) == 0 {
		zlog.Errorf(ctx, "HandleSceneTouchPrepare param invalid! p:%+v, msg:%+v", p, msg)
		return msg, components.ErrorParamInvalid
	}
	return
}

func startJobAndTasks(ctx *gin.Context, job *scenetouchmodel.TblSceneTouchPrepareJob, todoTaskList []*scenetouchmodel.TblSceneTouchPrepareTask) error {
	if job == nil {
		return components.ErrorParamInvalid
	}
	err := helpers.MysqlClientFuDao.WithContext(ctx).Transaction(func(tx *gorm.DB) (err error) {
		if job.Status == scenetouchmodel.JobStatusInit {
			job.Status = scenetouchmodel.JobStatusRunning
			job.StartTime = time.Now().Unix()
			job.UpdateTime = time.Now().Unix()
		}
		if err = tx.Save(job).Error; err != nil {
			return err
		}
		for _, task := range todoTaskList {
			if task == nil {
				return components.ErrorParamInvalid
			}
			if task.Status != scenetouchmodel.TaskStatusInit {
				task.Retry++
			}
			task.Status = scenetouchmodel.TaskStatusRunning
			task.UpdateTime = time.Now().Unix()
			if err = tx.Save(task).Error; err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func getJobLock(ctx *gin.Context, jobId int64, lockVal string) (lock bool, err error) {
	lockKey := fmt.Sprintf(RedisLockPrepareJob, jobId)
	return utils.GetSpinLock(ctx, lockKey, lockVal, 4, 32)
}

func releaseJobLock(ctx *gin.Context, jobId int64, lockVal string) {
	lockKey := fmt.Sprintf(RedisLockPrepareJob, jobId)
	success := false
	for k := 0; k < 3; k++ {
		o, e := utils.ReleaseLockByValue(ctx, lockKey, lockVal)
		if e == nil && o {
			success = true
			break
		}
		time.Sleep(10 * time.Millisecond)
	}
	if !success {
		zlog.Warnf(ctx, "scenetouch prepare job mq release lock failed! lockKey:%+v, lockVal:%+v", lockKey, lockVal)
	}
}

func setTaskSuccess(ctx *gin.Context, job *scenetouchmodel.TblSceneTouchPrepareJob, task *scenetouchmodel.TblSceneTouchPrepareTask, tx *gorm.DB) error {
	if job == nil || task == nil || tx == nil {
		return components.ErrorParamInvalid
	}
	if task.Status != scenetouchmodel.TaskStatusSuccess {
		job.SuccessNum++
		if task.Status == scenetouchmodel.TaskStatusFail {
			job.FailNum--
		}
	}
	if job.SuccessNum+job.FailNum == job.TotalNum {
		job.Status = scenetouchmodel.JobStatusDone
		job.EndTime = time.Now().Unix()
	}
	job.UpdateTime = time.Now().Unix()
	if err := tx.Save(job).Error; err != nil {
		return err
	}
	task.Status = scenetouchmodel.TaskStatusSuccess
	task.UpdateTime = time.Now().Unix()
	if err := tx.Save(task).Error; err != nil {
		return err
	}
	return nil
}

func setTaskFail(ctx *gin.Context, job *scenetouchmodel.TblSceneTouchPrepareJob, task *scenetouchmodel.TblSceneTouchPrepareTask, tx *gorm.DB) error {
	if job == nil || task == nil || tx == nil {
		return components.ErrorParamInvalid
	}
	if task.Status != scenetouchmodel.TaskStatusFail {
		job.FailNum++
		if task.Status == scenetouchmodel.TaskStatusSuccess {
			job.SuccessNum--
		}
	}
	if job.SuccessNum+job.FailNum == job.TotalNum {
		job.Status = scenetouchmodel.JobStatusDone
		job.EndTime = time.Now().Unix()
	}
	job.UpdateTime = time.Now().Unix()
	if err := tx.Save(job).Error; err != nil {
		return err
	}
	task.Status = scenetouchmodel.TaskStatusFail
	task.UpdateTime = time.Now().Unix()
	if err := tx.Save(task).Error; err != nil {
		return err
	}
	return nil
}
