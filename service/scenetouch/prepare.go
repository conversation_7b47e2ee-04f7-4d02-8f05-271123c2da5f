package scenetouch

import (
	"assistantdeskgo/api/dau"
	"assistantdeskgo/components"
	"assistantdeskgo/dto/scenetouch"
	"assistantdeskgo/helpers"
	scenetouchmodel "assistantdeskgo/models/scenetouch"
	"encoding/hex"
	"errors"
	"hash/crc64"
	"time"

	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"

	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"gorm.io/gorm"
)

var json = jsoniter.ConfigCompatibleWithStandardLibrary

func UseJob(ctx *gin.Context, req scenetouch.UseJobReq) (rsp scenetouch.UseJobRsp, err error) {
	if req.CourseId <= 0 || req.SceneType <= 0 || len(req.GroupList) == 0 {
		return rsp, components.ErrorParamInvalid
	}

	param<PERSON>ey, err := getParamKey(ctx, req)
	if err != nil {
		return rsp, components.ErrorDecode
	}

	userInfo := components.GetUserInfo(ctx)
	if userInfo == nil {
		return rsp, components.ErrorUserNotLogin
	}
	assistantUid := userInfo.SelectedBusinessUid
	if assistantUid <= 0 {
		return rsp, components.ErrorUserNotLogin
	}

	job, err := scenetouchmodel.TblSceneTouchPrepareJobRef.GetLatestValidJobByParamKey(ctx, paramKey, time.Now().Unix())
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return rsp, components.ErrorDbSelect
	}

	if job != nil {
		rsp.JobId = job.Id
		rsp.ParamKey = job.ParamKey
		return rsp, nil
	}

	job, tasks, err := buildJobAndTasksForInsert(ctx, req, assistantUid, paramKey)
	if err != nil {
		return rsp, err
	}

	groupTaskIds := make(map[string][]int64)
	err = helpers.MysqlClientFuDao.WithContext(ctx).Transaction(func(tx *gorm.DB) (err error) {
		if err = tx.Create(job).Error; err != nil {
			return err
		}
		for _, task := range tasks {
			task.JobId = job.Id
			if err = tx.Create(task).Error; err != nil {
				return err
			}
			groupTaskIds[task.GroupName] = append(groupTaskIds[task.GroupName], task.Id)
		}
		return nil
	})
	if err != nil {
		zlog.Infof(ctx, "UseJob insert error! job:%+v, tasks:%+v, err:%+v", job, tasks, err)
		return rsp, components.ErrorDbInsert
	}

	groupNeedVariables := make(map[string][]string)
	for _, group := range req.GroupList {
		groupNeedVariables[group.GroupName] = group.NeedVariables
	}

	for groupName, taskIds := range groupTaskIds {
		for _, subTaskIds := range fwyyutils.ChunkArrayInt64(taskIds, defaultBatchSize(ctx, req.SceneType)) {
			msg := scenetouch.PrepareTaskMQ{
				GroupName:     groupName,
				JobId:         job.Id,
				TaskIdList:    subTaskIds,
				NeedVariables: groupNeedVariables[groupName],
			}
			data := make(map[string]interface{})
			msgBytes, _ := json.Marshal(msg)
			_ = json.Unmarshal(msgBytes, &data)
			zlog.Infof(ctx, "UseJob mq data:%+v", data)
			res, _err := rmq.SendCmd(ctx, "sceneTouchPrepare", 214204, "core", "zb", data, "")
			if _err != nil {
				zlog.Warnf(ctx, "UseJob mq send error! data:%+v, res:%+v, err:%+v", data, res, err)
				err = helpers.MysqlClientFuDao.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
					job.Status = scenetouchmodel.JobStatusCancel
					job.UpdateTime = time.Now().Unix()
					return tx.Save(job).Error
				})
				if err != nil {
					zlog.Infof(ctx, "UseJob update job status error! jobId:%d, err:%+v", job.Id, err)
				}
				return rsp, components.DefaultError("任务创建失败")
			}
			zlog.Debugf(ctx, "UseJob mq send success! data:%+v, res:%+v", data, res)
		}
	}

	rsp.JobId = job.Id
	rsp.ParamKey = job.ParamKey
	return rsp, nil
}

func defaultExpireTime(ctx *gin.Context, sceneType int64) (expireTime int64) {
	expireTime = time.Now().Unix() + 5
	return
}

func defaultBatchSize(ctx *gin.Context, sceneType int64) (batchSize int) {
	batchSize = 10
	return
}

func getParamKey(ctx *gin.Context, req scenetouch.UseJobReq) (key string, err error) {
	jsonStr, err := json.MarshalToString(req)
	if err != nil {
		return "", err
	}
	table := crc64.MakeTable(crc64.ISO)
	hashValue := crc64.Checksum([]byte(jsonStr), table)
	hashBytes := make([]byte, 8)
	for i := 0; i < 8; i++ {
		hashBytes[i] = byte(hashValue >> (8 * i))
	}
	key = hex.EncodeToString(hashBytes)
	return key, nil
}

func buildJobAndTasksForInsert(ctx *gin.Context, req scenetouch.UseJobReq, assistantUid int64, paramKey string) (job *scenetouchmodel.TblSceneTouchPrepareJob, tasks []*scenetouchmodel.TblSceneTouchPrepareTask, err error) {
	if req.SceneType == 0 {
		return nil, nil, errors.New("sceneType不能为空")
	}
	groupList, err := json.MarshalToString(req.GroupList)
	if err != nil {
		return nil, nil, components.ErrorDecode
	}
	sceneContext := ""
	if req.SceneContext != "" {
		_, _err := parseSceneContext(ctx, int(req.SceneType), req.SceneContext)
		if _err != nil {
			zlog.Infof(ctx, "parseSceneContext error! sceneType:%d, sceneContext:%s, err:%+v", req.SceneType, req.SceneContext, _err)
			return nil, nil, components.DefaultError("场景上下文解析错误")
		}
	}
	sceneContext = req.SceneContext

	usedGroupName := make(map[string]bool)
	totalNum := 0
	for _, group := range req.GroupList {
		if usedGroupName[group.GroupName] {
			return nil, nil, components.DefaultError("groupName重复")
		}
		usedGroupName[group.GroupName] = true
		for _, studentUid := range group.StudentUids {
			tasks = append(tasks, &scenetouchmodel.TblSceneTouchPrepareTask{
				GroupName:  group.GroupName,
				StudentUid: studentUid,
				Status:     scenetouchmodel.TaskStatusInit,
				CreateTime: time.Now().Unix(),
				UpdateTime: time.Now().Unix(),
			})
			totalNum++
		}
	}

	job = &scenetouchmodel.TblSceneTouchPrepareJob{
		AssistantUid: assistantUid,
		CourseId:     req.CourseId,
		LessonId:     req.LessonId,
		SceneType:    int(req.SceneType),
		GroupList:    groupList,
		SceneContext: sceneContext,
		ParamKey:     paramKey,
		TotalNum:     totalNum,
		Status:       scenetouchmodel.JobStatusInit,
		ExpireTime:   defaultExpireTime(ctx, req.SceneType),
		Creator:      assistantUid,
		Updater:      assistantUid,
		CreateTime:   time.Now().Unix(),
		UpdateTime:   time.Now().Unix(),
	}
	return job, tasks, nil
}

func JobStatus(ctx *gin.Context, req scenetouch.JobStatusReq) (rsp scenetouch.JobStatusRsp, err error) {
	if req.JobId <= 0 {
		return rsp, components.ErrorParamInvalid
	}

	job, err := scenetouchmodel.TblSceneTouchPrepareJobRef.GetJobById(ctx, req.JobId, false)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return rsp, components.ErrorDbSelect
		}
		return rsp, components.ErrorDbSelect
	}

	rsp = scenetouch.JobStatusRsp{
		JobId:         job.Id,
		CreateTime:    job.CreateTime,
		StartTime:     job.StartTime,
		EndTime:       job.EndTime,
		SuccessNum:    job.SuccessNum,
		FailNum:       job.FailNum,
		TotalNum:      job.TotalNum,
		RunStatusDesc: getJobStatusDesc(job),
		Status:        job.Status,
		UpdateTime:    job.UpdateTime,
		ExpireTime:    job.ExpireTime,
	}
	return rsp, nil
}

func getJobStatusDesc(job *scenetouchmodel.TblSceneTouchPrepareJob) string {
	if job.SuccessNum+job.FailNum < job.TotalNum {
		return "生成中"
	} else if job.SuccessNum >= job.TotalNum {
		return "全部成功"
	} else if job.FailNum >= job.TotalNum {
		return "全部失败"
	} else if job.FailNum > 0 && job.SuccessNum+job.FailNum == job.TotalNum {
		return "部分成功"
	}
	return ""
}

func JobResult(ctx *gin.Context, req scenetouch.JobResultReq) (rsp scenetouch.JobResultRsp, err error) {
	if req.JobId <= 0 {
		return rsp, components.ErrorParamInvalid
	}

	job, err := scenetouchmodel.TblSceneTouchPrepareJobRef.GetJobById(ctx, req.JobId, false)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return rsp, components.ErrorDbSelect
		}
		return rsp, components.ErrorDbSelect
	}

	rsp = scenetouch.JobResultRsp{
		JobId:        job.Id,
		CourseId:     job.CourseId,
		LessonId:     job.LessonId,
		AssistantUid: job.AssistantUid,
		SceneType:    job.SceneType,
		SceneContext: job.SceneContext,
		TaskResults:  make([]scenetouch.TaskResultsItem, 0),
	}

	taskRets := make([]scenetouch.TaskResultsItem, 0)
	taskList, err := scenetouchmodel.TblSceneTouchPrepareTaskRef.GetTaskListByJobId(ctx, req.JobId, req.OnlyFail)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			zlog.Infof(ctx, "JobResult taskList not found! jobId:%d", req.JobId)
			return rsp, components.ErrorDbSelect
		}
		return rsp, components.ErrorDbSelect
	}

	studentUids := make([]int64, 0)
	for _, task := range taskList {
		studentUids = append(studentUids, task.StudentUid)
	}
	if len(studentUids) == 0 {
		zlog.Infof(ctx, "JobResult studentUids is empty! jobId:%d", req.JobId)
		return rsp, nil
	}
	studentMap, err := dau.GetStudents(ctx, studentUids, []string{"studentUid", "studentName"})
	if err != nil {
		zlog.Infof(ctx, "dau.GetStudents error! studentUids:%+v", studentUids)
		return rsp, components.DefaultError("获取学生信息失败")
	}

	for _, task := range taskList {
		vars := make(map[string]interface{})
		if task.Variables != "" {
			err = json.UnmarshalFromString(task.Variables, &vars)
			if err != nil {
				return rsp, components.ErrorDecode
			}
		}
		extra := scenetouch.PrepareTaskExtra{}
		if task.Extra != "" {
			err = json.UnmarshalFromString(task.Extra, &extra)
			if err != nil {
				return rsp, components.ErrorDecode
			}
		}

		studentName := ""
		if studentMap != nil {
			studentName = studentMap[task.StudentUid].StudentName
		}
		taskRet := scenetouch.TaskResultsItem{
			GroupName:   task.GroupName,
			StudentUid:  task.StudentUid,
			StudentName: studentName,
			Status:      task.Status,
			Variables:   vars,
			FailReasons: extra.FailReasons,
		}
		taskRets = append(taskRets, taskRet)
	}
	rsp.TaskResults = taskRets

	return rsp, nil
}
