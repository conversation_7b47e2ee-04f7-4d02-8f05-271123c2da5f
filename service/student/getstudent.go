package student

import (
	"assistantdeskgo/api/achilles"
	allocateApi "assistantdeskgo/api/allocate"
	"assistantdeskgo/api/dal"
	"assistantdeskgo/api/dau"
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtostudent"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/allocate"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/arkgo"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/das"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"go.uber.org/ratelimit"
	"math"
	"time"
)

func getLastYearTimeStamp() int64 {
	return time.Now().Unix() - 3600*360*24
}

func GetStudentByPhone(ctx *gin.Context, req dtostudent.GetStudentByPhoneReq) (*dtostudent.GetStudentByPhoneRsp, error) {
	if req.AssistantUid <= 0 || len(req.Phone) <= 0 {
		return nil, components.ErrorParamInvalid
	}

	leadsRsp, err := allocate.GetValidLeadsByDeviceId(ctx, allocate.GetValidLeadsByDeviceIdReq{
		DeviceId: req.AssistantUid,
		RegTime:  getLastYearTimeStamp(),
	})
	if err != nil {
		return nil, err
	}

	rsp := &dtostudent.GetStudentByPhoneRsp{
		StudentInfos: make([]dtostudent.GetStudentInfo, 0),
	}

	if leadsRsp == nil || len(leadsRsp.List) == 0 {
		return rsp, nil
	}

	studentIdSet := fwyyutils.NewInt64Set()
	for _, info := range leadsRsp.List {
		if info.Status != allocate.LeadsStatusOK {
			continue
		}
		studentIdSet.Add(info.StudentUid)
	}

	studentIds := studentIdSet.AsList()
	zlog.Infof(ctx, "[GetStudentByPhone] get student by phone, ids: %s", fwyyutils.MarshalIgnoreError(studentIds))
	studentsMap, err := dau.GetStudents(ctx, studentIds, []string{"studentUid", "studentName", "registerPhone", "avatar"})
	if err != nil {
		return nil, err
	}

	for _, studentInfo := range studentsMap {
		// 匹配手机后四位
		regPhone := studentInfo.RegisterPhone
		if regPhone[len(regPhone)-4:] != req.Phone[fwyyutils.MaxInt(len(req.Phone)-4, 0):] {
			continue
		}
		rsp.StudentInfos = append(rsp.StudentInfos, dtostudent.GetStudentInfo{
			StudentUid:  int64(studentInfo.StudentUid),
			Phone:       regPhone,
			StudentName: studentInfo.StudentName,
			Avatar:      studentInfo.Avatar,
		})
	}
	return rsp, nil
}

func GetStudentByUid(ctx *gin.Context, req dtostudent.GetStudentByUidReq) (*dtostudent.GetStudentByUidRsp, error) {
	if req.AssistantUid <= 0 || req.StudentUid <= 0 {
		return nil, components.ErrorParamInvalid
	}

	leadsRsp, err := allocate.GetValidLeadsByDeviceId(ctx, allocate.GetValidLeadsByDeviceIdReq{
		DeviceId:   req.AssistantUid,
		StudentUid: req.StudentUid,
		RegTime:    getLastYearTimeStamp(),
	})
	if err != nil {
		return nil, err
	}

	rsp := &dtostudent.GetStudentByUidRsp{
		StudentInfos: make([]dtostudent.GetStudentInfo, 0),
	}
	if leadsRsp == nil || len(leadsRsp.List) == 0 {
		return rsp, nil
	}

	studentIds := make([]int64, 0)
	for _, info := range leadsRsp.List {
		if info.Status != allocate.LeadsStatusOK {
			continue
		}
		studentIds = append(studentIds, info.StudentUid)
	}

	if len(studentIds) == 0 {
		return rsp, nil
	}

	studentsMap, err := dau.GetStudents(ctx, []int64{req.StudentUid}, []string{"studentUid", "studentName", "registerPhone", "avatar"})
	if err != nil {
		return nil, err
	}

	for _, studentInfo := range studentsMap {
		rsp.StudentInfos = append(rsp.StudentInfos, dtostudent.GetStudentInfo{
			StudentUid:  int64(studentInfo.StudentUid),
			Phone:       studentInfo.RegisterPhone,
			StudentName: studentInfo.StudentName,
			Avatar:      studentInfo.Avatar,
		})
	}
	return rsp, nil
}

var GetStudentDataAPILimiter ratelimit.Limiter

func init() {
	GetStudentDataAPILimiter = ratelimit.New(30, ratelimit.WithoutSlack)
}

func GetStudentData(ctx *gin.Context, req dtostudent.GetStudentDataReq) (res *dtostudent.GetStudentDataRsp, err error) {
	GetStudentDataAPILimiter.Take()

	res = &dtostudent.GetStudentDataRsp{
		StudentUid: req.StudentUid,
	}

	// 获取学生的 leads 信息数据，没有就不传
	stuLeads, err := allocateApi.GetLeadsByUid(ctx, req.StudentUid)
	if err != nil {
		// 不阻塞
		zlog.Warnf(ctx, "allocate GetStudentData fail,studentUid=%d,err=%s", req.StudentUid, err.Error())
	}
	var leadsId, assistantUid, personUid int64
	for _, item := range stuLeads {
		if item.CourseId == req.CourseId {
			leadsId = int64(item.LeadsId)
		}
	}
	// 老师的信息
	teacherInfo, err := achilles.GeTaklsWxInfo(ctx, req.CourseId, req.StudentUid)
	if err != nil {
		// 不阻塞
		zlog.Warnf(ctx, "GeTaklsWxInfo fail,studentUid=%d,courseId=%dmerr=%s", req.StudentUid, req.StudentUid, err.Error())
	}

	assistantUid = teacherInfo.LpcUid
	personUid = teacherInfo.PersonUid

	// 方舟获取数据
	formatData, err := arkgo.ArkFormat(ctx, arkgo.ArkFormatReq{
		AssistantUid:         assistantUid,
		PersonUid:            personUid,
		CourseId:             req.CourseId,
		LessonId:             req.LessonId,
		LeadIds:              []int64{leadsId},
		StudentUids:          []int64{req.StudentUid},
		LeadsIdMapStudentUid: map[int64]int64{leadsId: req.StudentUid}, // 必须
		FormatKeys: []string{"lessonWatchTime", "roomAttendDuration", "lbpPlayContentTime",
			"lessonIsFinishedTheCourse", "xlbcHomeworkCorrectStatus", "xlbcHomeworkCorrectTime", "xlbcHomeworkSubmitTime",
			"xlbcHomeworkLevel", "xlbcHomeworkSubmitStatus", "playbackParticipateCnt", "inclassParticipateCnt"}, // 先写死
	})
	if err != nil {
		return nil, err
	}

	// 返回包装后的结果
	fieldData, ok := formatData.StudentList[req.StudentUid]
	if ok {
		res = &dtostudent.GetStudentDataRsp{
			StudentUid:                cast.ToInt64(fieldData["studentUid"]),
			LessonWatchTime:           cast.ToInt64(fieldData["lessonWatchTime"]),
			RoomAttendDuration:        cast.ToString(fieldData["roomAttendDuration"]),
			PlayContentTime:           cast.ToString(fieldData["playContentTime"]),
			LessonIsFinishedTheCourse: cast.ToInt64(fieldData["lessonIsFinishedTheCourse"]),
			InclassParticipateCnt:     cast.ToInt64(fieldData["inclassParticipateCnt"]),
			PlaybackParticipateCnt:    cast.ToInt64(fieldData["playbackParticipateCnt"]),
			XlbcHomeworkCorrectStatus: cast.ToInt64(fieldData["xlbcHomeworkCorrectStatus"]),
			XlbcHomeworkCorrectTime:   cast.ToInt64(fieldData["xlbcHomeworkCorrectTime"]),
			XlbcHomeworkLevel:         cast.ToInt64(fieldData["xlbcHomeworkLevel"]),
			XlbcHomeworkSubmitStatus:  cast.ToInt64(fieldData["xlbcHomeworkSubmitStatus"]),
			XlbcHomeworkSubmitTime:    cast.ToInt64(fieldData["xlbcHomeworkSubmitTime"]),
		}
	}

	// 基础数据获取
	studentInfoMap, err := dau.GetStudents(ctx, []int64{req.StudentUid}, []string{"studentUid", "studentName"})
	if err != nil {
		return
	}
	courseField := []string{"courseId", "courseName"}
	lessonField := []string{"lessonId", "lessonName"}
	courseInfo, err := dal.GetCourseLessonInfoByCourseId(ctx, req.CourseId, courseField, lessonField)
	if err != nil {
		return
	}

	dasRes, err := das.GetStuCourseByConds(ctx, req.StudentUid, []int64{57, 58, 59}, []int64{}, []int64{}, []int64{101})
	if err != nil {
		return nil, err
	}

	if len(dasRes.Data) > 0 {
		res.LearnCodeDayCnt = int64(math.Ceil(float64((time.Now().Unix() - dasRes.Data[0].AllStartTime) / 86400)))
	}

	res.StudentName = studentInfoMap[req.StudentUid].StudentName
	res.AssistantDeviceName = teacherInfo.NickName
	res.AssistantPersonName = teacherInfo.LpcName
	res.CourseName = courseInfo.CourseName

	return
}
