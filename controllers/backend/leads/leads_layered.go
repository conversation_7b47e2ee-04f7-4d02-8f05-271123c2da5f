package leads

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtoleads"
	"assistantdeskgo/service/leads"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

func GetLeadsLayerDetailInfo(ctx *gin.Context) {
	req := dtoleads.GetLeadsLayerDetailInfoReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := leads.GetLeadsLayerDetailInfo(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, rsp)
}
