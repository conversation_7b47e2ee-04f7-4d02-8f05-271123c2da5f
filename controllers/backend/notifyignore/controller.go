package notifyignore

import (
	"assistantdeskgo/dto/dtonotifyignore"
	"assistantdeskgo/service/notifyignore"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

// AddNotifyIgnore 添加消息免打扰配置
func AddNotifyIgnore(ctx *gin.Context) {
	param := dtonotifyignore.AddNotifyIgnoreReq{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	result, err := notifyignore.AddNotifyIgnore(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

// DeleteNotifyIgnore 删除消息免打扰配置
func DeleteNotifyIgnore(ctx *gin.Context) {
	param := dtonotifyignore.DeleteNotifyIgnoreReq{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	result, err := notifyignore.DeleteNotifyIgnore(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

// ListNotifyIgnore 获取消息免打扰配置列表
func ListNotifyIgnore(ctx *gin.Context) {
	param := dtonotifyignore.ListNotifyIgnoreReq{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	result, err := notifyignore.ListNotifyIgnore(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}
