package notice

import (
	"assistantdeskgo/dto/dtonotice"
	"assistantdeskgo/service/notice"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

func GetUnReadNoticeList(ctx *gin.Context) {
	result, err := notice.GetUnReadNoticeList(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func GetTopNoticeList(ctx *gin.Context) {
	result, err := notice.GetTopNoticeList(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func GetNoticeDetail(ctx *gin.Context) {
	param := dtonotice.UserNoticeDeatilParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	result, err := notice.GetNoticeDetail(ctx, param.NoticeId)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func UserNoticeFeedback(ctx *gin.Context) {
	param := dtonotice.UserNoticeFeedbackParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	result, err := notice.UserNoticeFeedback(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func UserNoticeComment(ctx *gin.Context) {
	param := dtonotice.UserNoticeCommentParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	result, err := notice.ReplyNoticeComment(ctx, param, false)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func UserNoticeCommentLike(ctx *gin.Context) {
	param := dtonotice.UserNoticeCommentLikeParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	result, err := notice.UserNoticeCommentLike(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func UserNoticeList(ctx *gin.Context) {
	param := dtonotice.UserNoticeListParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	result, err := notice.GetUserNoticeList(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func UserNoticeReadLater(ctx *gin.Context) {
	param := dtonotice.UserNoticeReadParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	result, err := notice.UserNoticeReadLater(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func UserNoticeReadTime(ctx *gin.Context) {
	param := dtonotice.UserNoticeReadParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	result, err := notice.UserNoticeReadTime(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func UserNoticeReadAll(ctx *gin.Context) {
	result, err := notice.UserNoticeReadAll(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

