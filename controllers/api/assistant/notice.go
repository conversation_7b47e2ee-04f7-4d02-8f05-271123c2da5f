package assistant

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtoassistant"
	"assistantdeskgo/service/assistant"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func AddAssistantNotice(ctx *gin.Context) {
	req := dtoassistant.AddAssistantNoticeReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "AddAssistantNotice params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	resp, err := assistant.AddAssistantNotice(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "AddAssistantNotice AddAssistantNotice error, err: %+v", err)
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
}

func ReadAllAssistantNotice(ctx *gin.Context) {
	resp, err := assistant.ReadAllAssistantNotice(ctx)
	if err != nil {
		zlog.Warnf(ctx, "ReadAllAssistantNotice error, err: %+v", err)
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
}
