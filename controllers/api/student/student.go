package student

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtostudent"
	"assistantdeskgo/service/student"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetStudentByPhone(ctx *gin.Context) {
	req := dtostudent.GetStudentByPhoneReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "GetStudentByPhone params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	resp, err := student.GetStudentByPhone(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, resp)
}

func GetStudentByUid(ctx *gin.Context) {
	req := dtostudent.GetStudentByUidReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "GetStudentByUid params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	resp, err := student.GetStudentByUid(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, resp)
}

func GetStudentData(ctx *gin.Context) {
	req := dtostudent.GetStudentDataReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "GetStudentByUid params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	resp, err := student.GetStudentData(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, resp)
}
