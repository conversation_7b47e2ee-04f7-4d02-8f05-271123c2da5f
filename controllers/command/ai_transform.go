package command

import (
	"assistantdeskgo/api/aiturbo"
	"assistantdeskgo/api/dal"
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/api/muse"
	"assistantdeskgo/api/tower"
	"assistantdeskgo/api/transcribe"
	"assistantdeskgo/defines"
	"assistantdeskgo/helpers"
	"assistantdeskgo/models"
	"assistantdeskgo/service/ai"
	"assistantdeskgo/service/gray"
	"assistantdeskgo/utils"
	"encoding/json"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	utils2 "gorm.io/gorm/utils"
	"os"
	"strings"
	"time"
)

const REDIS_AI_TASK_NAME = "assistantdeskgo:ai_task_lock"
const TASK_INTERVAL = 60

type ESCallResultDump struct {
	CallId      json.Number `json:"call_id"`
	JsonContent string      `json:"json_content"`
	Dt          string      `json:"dt"`
}

type AudioContentResult struct {
	DownloadUrl  string         `json:"download_url"`
	SampleMark   bool           `json:"sample_mark"`
	CallTime     int            `json:"call_time"`
	Duration     int            `json:"duration"`
	SentenceList []SentenceList `json:"sentence_list"`
}

type SentenceList struct {
	SentenceId int    `json:"sentence_id"`
	Role       int    `json:"role"`
	Content    string `json:"content"`
}

func AiTaskTransform(ctx *gin.Context) error {

	clusterName := os.Getenv("CLUSTER_NAME")
	zlog.Infof(ctx, "当前所在集群,env:%s", clusterName)
	if env.GetRunEnv() == env.RunEnvTips || strings.Contains(clusterName, defines.ENV_STABLE_KEY) || strings.Contains(clusterName, defines.ENV_SMALL_KEY) {
		zlog.Infof(ctx, "当前所在集群跳过,env:%s", clusterName)
		return nil
	}

	lock, err := helpers.RedisClient.SetNxByEX(ctx, REDIS_AI_TASK_NAME, 1, uint64(TASK_INTERVAL))
	if err != nil {
		zlog.Warnf(ctx, "Get Redis Lock fail,err=%v", err)
		return nil
	}
	if !lock {
		zlog.Infof(ctx, "Not acquired lock,skip")
		return nil
	}

	zlog.Infof(ctx, "start AiTaskTransform ...")

	processExpire(ctx)    //过期
	processAi(ctx)        //处理AI接口
	processPostAudio(ctx) //处理语音

	return nil
}

func processPostAudio(ctx *gin.Context) {
	zlog.Infof(ctx, "start processAi ...")
	now := time.Now().Unix()
	startTime := now - 1*defines.SECOND_OF_DAY
	endTime := now - 30*defines.SECOND_OF_MINUTE
	for i := 0; i < 20; i++ {

		taskList, err := models.AiCallRecordRef.ListByTimeAndStatus(ctx, startTime, endTime, []int64{models.STATUS_DEFAULT, models.STATUS_CALL_AUDIO}, int64(i))
		if err != nil {
			zlog.Warnf(ctx, "processPostAudio fail,startTime=%v,endTime=%v,err=%v", startTime, endTime, err)
			continue
		}
		if len(taskList) == 0 {
			continue
		}

		reqList := []transcribe.AudioReqModel{}
		callIDList := []int64{}
		//请求语音接口
		for _, task := range taskList {
			if task.Duration > defines.THREE_MINITE_OF_MILLI_SECOND { //大于3分钟
				if now-(task.StartTime/1000) <= defines.SECOND_OF_HOUR*2 {
					zlog.Infof(ctx, "processPostAudio audio retry not full hour")
					continue
				}
			}

			link, err := muse.GetLink(ctx, muse.GetRecordLinkReq{
				Path:    task.RecordFile,
				Type:    task.ResourceType,
				Day:     1,
				IsInner: 1,
			})
			if err != nil {
				zlog.Warnf(ctx, "processPostAudio getLink Fail,callId=%v,err=%v", task.CallId, err)
				continue
			}
			if link.Link == "" {
				zlog.Warnf(ctx, "processPostAudio getLink is empty,callId=%v", task.CallId)
				continue
			}

			reqList = append(reqList, transcribe.AudioReqModel{
				defines.AI_BUSSINE_CALL_TYPE + "_" + utils2.ToString(task.StudentUid) + "_" + utils2.ToString(task.CallId),
				link.Link,
				task.Duration,
			})
			callIDList = append(callIDList, task.CallId)
			if len(reqList) >= 20 {
				err = transcribe.ReqTranscribe(ctx, reqList)
				if err != nil {
					zlog.Warnf(ctx, "processPostAudio ReqTranscribe fail,data=%+v,err=%v", reqList, err)
					continue
				}

				err = models.AiCallRecordRef.UpdateStatus(ctx, callIDList, models.STATUS_CALL_AUDIO, int64(i))
				if err != nil {
					zlog.Warnf(ctx, "processPostAudio UpdateStatus fail,data=%+v,err=%v", callIDList, err)
					continue
				}
				reqList = []transcribe.AudioReqModel{}
				callIDList = []int64{}
			}
		}
		if len(reqList) > 0 {
			err = transcribe.ReqTranscribe(ctx, reqList)
			if err != nil {
				zlog.Warnf(ctx, "processPostAudio ReqTranscribe fail,data=%+v,err=%v", reqList, err)
				return
			}

			err = models.AiCallRecordRef.UpdateStatus(ctx, callIDList, models.STATUS_CALL_AUDIO, int64(i))
			if err != nil {
				zlog.Warnf(ctx, "processPostAudio UpdateStatus fail,data=%+v,err=%v", callIDList, err)
			}
		}
	}

}

func processAi(ctx *gin.Context) {
	zlog.Infof(ctx, "start processAi ...")
	now := time.Now().Unix()
	startTime := now - 1*defines.SECOND_OF_DAY
	endTime := now - 15*defines.SECOND_OF_MINUTE
	for i := 0; i < 20; i++ {
		taskList, err := models.AiCallRecordRef.ListByTimeAndStatus(ctx, startTime, endTime, []int64{models.STATUS_CALL_AUDIO_FINISH, models.STATUS_TEXT_ABSTRACT}, int64(i))
		if err != nil {
			zlog.Warnf(ctx, "ListByTimeAndStatus fail,startTime=%v,endTime=%v,err=%v", startTime, endTime, err)
			continue
		}

		postText(ctx, taskList)
	}

}

func postText(ctx *gin.Context, taskList []models.AiCallRecord) {
	now := time.Now().Unix()
	var courseIdList []int64
	var teacherUidList []int64
	for _, task := range taskList {
		if task.CourseId > 0 {
			courseIdList = append(courseIdList, task.CourseId)
			teacherUidList = append(teacherUidList, task.DeviceUid)
		}
	}

	courseIdMap, err := dal.GetCourseLessonInfoByCourseIds(ctx, utils.FilterInt64Duplicates(courseIdList), []string{}, []string{})
	if err != nil {
		return
	}
	teacherMap := map[string]mesh.DeviceInfo{}
	teacherUidList = utils.FilterInt64Duplicates(teacherUidList)
	for _, subTeacherUidList := range utils.SplitInt64Array(teacherUidList, 90) {
		subTeacherMap, err := mesh.GetDeviceInfoListByDeviceUidList(ctx, subTeacherUidList)
		if err != nil {
			return
		}
		for k, v := range subTeacherMap {
			teacherMap[k] = v
		}
	}

	for _, task := range taskList {
		req := aiturbo.PostTextAbstractReq{}
		req.Calltype = task.CallMode
		req.CourseId = utils2.ToString(task.CourseId)
		req.CallId = utils2.ToString(task.CallId)
		req.Source = defines.AI_TOKEN
		req.Token = defines.AI_TOKEN
		req.Topic = ai.GetTopic(task.SourceType)
		req.TeacherId = utils2.ToString(task.DeviceUid)
		req.StudentUid = utils2.ToString(task.StudentUid)
		req.DeviceNumber = []aiturbo.DeviceNumber{{Number: utils2.ToString(task.DeviceUid), Type: "in"}, {Number: utils2.ToString(task.StudentUid), Type: "out"}}
		info, ok := teacherMap[utils2.ToString(task.DeviceUid)]
		if ok {
			if utils.InArrayInt(defines.ROLE_TEACHER_DUXUE, info.KpAscriptionList) {
				req.Teacherjs = 1
			}
		}
		courseId := task.CourseId
		if courseId > 0 {
			courseInfo, ok := courseIdMap[courseId]
			if ok {
				req.Xuebu = defines.Grade2DepartMap[courseInfo.MainGradeId]
				req.Classname = courseInfo.CourseName
			}
		}
		var sentenceList []SentenceList
		if strings.HasSuffix(task.Content, ".txt") {
			content, err := helpers.BaiduBucket2.DownloadContent(ctx, task.Content)
			if err != nil {
				zlog.Warnf(ctx, "processAi download fail,callId=%v,content=%v", task.CallId, task.Content)
				continue
			}
			err = json.Unmarshal(content, &sentenceList)
			if err != nil {
				zlog.Warnf(ctx, "processAi Unmarshal fail,callId=%v,content=%v", task.CallId, task.Content)
				continue
			}
		} else {

			err = json.Unmarshal([]byte(task.Content), &sentenceList)
			if err != nil {
				zlog.Warnf(ctx, "processAi Unmarshal fail,callId=%v,content=%v", task.CallId, task.Content)
				continue
			}
		}
		var contentReq []aiturbo.PostTextAbstractReqContent
		for _, sentence := range sentenceList {
			contentReq = append(contentReq, aiturbo.PostTextAbstractReqContent{
				Sentenceid: sentence.SentenceId,
				Content:    sentence.Content,
				Role:       sentence.Role,
			})
		}
		req.Contents = contentReq
		coursePriceTag, err := tower.GetCourseInfo(ctx, task.CourseId)
		if err != nil {
			continue
		}
		req.PriceTag = coursePriceTag.CoursePriceTag

		if env.GetRunEnv() == env.RunEnvTips || gray.SwitchAbstractText(ctx, req.PriceTag, defines.Config_AI_TEXT_ABSTRACT_GRAY) {
			err = aiturbo.PostTextAbstractV3(ctx, req)
		} else {
			err = aiturbo.PostTextAbstractV2(ctx, req)
		}

		if err != nil {
			zlog.Warnf(ctx, "processAi PostTextAbstract fail,req=%v,err=%v", req, err)
			continue
		} else {
			zlog.Infof(ctx, "processAi PostTextAbstract Success callId=%v", task.CallId)
			task.Status = models.STATUS_TEXT_ABSTRACT
			task.UpdateTime = now
			err := models.AiCallRecordRef.Updates(ctx, []models.AiCallRecord{task}, task.StudentUid)
			if err != nil {
				zlog.Warnf(ctx, "processAi Update fail,callId=%v,err=%v", task.CallId, err)
				continue
			}
		}
	}
}

func processExpire(ctx *gin.Context) {
	zlog.Infof(ctx, "start processExpire ...")
	//查询一天没有处理
	now := time.Now().Unix()
	startTime := now - 2*defines.SECOND_OF_DAY
	endTime := now - 1*defines.SECOND_OF_DAY

	for i := 0; i < 20; i++ {
		taskList, err := models.AiCallRecordRef.ListByTimeAndStatus(ctx, startTime, endTime, []int64{models.STATUS_DEFAULT, models.STATUS_CALL_AUDIO, models.STATUS_CALL_AUDIO_FINISH, models.STATUS_TEXT_ABSTRACT}, int64(i))
		if err != nil {
			zlog.Warnf(ctx, "ListByTimeAndStatus fail,startTime=%v,endTime=%v,err=%v", startTime, endTime, err)
			continue
		}
		if len(taskList) == 0 {
			continue
		}
		var callIdList []int64
		for _, task := range taskList {
			callIdList = append(callIdList, task.CallId)
		}
		zlog.Infof(ctx, "AiTaskTransform mark expire,callIdList=%v", callIdList)
		err = models.AiCallRecordRef.UpdateStatus(ctx, callIdList, models.STATUS_EXPIRE, int64(i))
		if err != nil {
			zlog.Warnf(ctx, "AiTaskTransform mark expire fail,callIdList=%v,err=%v", callIdList, err)

		}
	}
}
